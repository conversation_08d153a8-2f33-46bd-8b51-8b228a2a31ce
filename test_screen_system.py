#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار نظام الشاشات الجديد
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# استيراد نظام النوافذ المنبثقة الجديد
from ModernUI import ModernPopupManager

class TestScreen1(QWidget):
    """شاشة اختبار 1"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        title = QLabel("🏠 الشاشة الرئيسية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background: #ecf0f1;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        content = QLabel("هذه هي الشاشة الرئيسية للتطبيق.\nيمكنك التنقل بين الشاشات باستخدام الأزرار في الأعلى.")
        content.setAlignment(Qt.AlignCenter)
        content.setWordWrap(True)
        layout.addWidget(content)
        
        layout.addStretch()
        self.setLayout(layout)

class TestScreen2(QWidget):
    """شاشة اختبار 2"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        title = QLabel("📊 شاشة البيانات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        content = QLabel("هذه شاشة عرض البيانات.\nهنا يمكن عرض جداول البيانات والمعلومات.")
        content.setAlignment(Qt.AlignCenter)
        content.setWordWrap(True)
        layout.addWidget(content)
        
        # إضافة بعض الأزرار للاختبار
        btn_layout = QVBoxLayout()
        for i in range(3):
            btn = QPushButton(f"زر اختبار {i+1}")
            btn.setStyleSheet("""
                QPushButton {
                    padding: 10px;
                    background: #3498db;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #2980b9;
                }
            """)
            btn_layout.addWidget(btn)
        
        layout.addLayout(btn_layout)
        layout.addStretch()
        self.setLayout(layout)

class TestScreen3(QWidget):
    """شاشة اختبار 3"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        title = QLabel("⚙️ شاشة الإعدادات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title)
        
        content = QLabel("هذه شاشة الإعدادات.\nهنا يمكن تخصيص إعدادات التطبيق.")
        content.setAlignment(Qt.AlignCenter)
        content.setWordWrap(True)
        layout.addWidget(content)
        
        layout.addStretch()
        self.setLayout(layout)

class TestPopupApp(QWidget):
    """تطبيق اختبار نظام النوافذ المنبثقة"""
    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("اختبار نظام النوافذ المنبثقة الجديد")
        self.setGeometry(100, 100, 900, 700)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء مدير النوافذ المنبثقة
        self.popup_manager = ModernPopupManager()

        # إضافة النوافذ المنبثقة
        screen1 = TestScreen1()
        screen2 = TestScreen2()
        screen3 = TestScreen3()

        self.popup_manager.add_popup_option(screen1, "الشاشة الرئيسية", "🏠", "الصفحة الرئيسية للتطبيق")
        self.popup_manager.add_popup_option(screen2, "شاشة البيانات", "📊", "عرض وإدارة البيانات")
        self.popup_manager.add_popup_option(screen3, "شاشة الإعدادات", "⚙️", "تخصيص إعدادات التطبيق")

        layout.addWidget(self.popup_manager)
        self.setLayout(layout)

def main():
    """الدالة الرئيسية للاختبار"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont()
    font.setFamily('Arial')
    font.setPointSize(10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = TestPopupApp()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
