"""
وحدة الاستيراد الذكية المحسنة
تدعم استيراد البيانات من تنسيقات متعددة مع التحقق من صحة البيانات
"""

import sqlite3
import pandas as pd
import json
import csv
from datetime import datetime
import os
import re
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QComboBox, QCheckBox, QGroupBox, QFormLayout, QTextEdit,
    QProgressBar, QMessageBox, QTabWidget, QSplitter,
    QListWidget, QListWidgetItem, QFrame, QScrollArea,
    QSpinBox, QLineEdit
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
import chardet

class DataValidator:
    """فئة للتحقق من صحة البيانات"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        
    def validate_activity_data(self, data):
        """التحقق من صحة بيانات الأنشطة"""
        self.errors = []
        self.warnings = []
        
        required_fields = ['person', 'activity_name', 'activity_date']
        
        for index, row in data.iterrows():
            row_errors = []
            row_warnings = []
            
            # التحقق من الحقول المطلوبة
            for field in required_fields:
                if field not in row or pd.isna(row[field]) or str(row[field]).strip() == '':
                    row_errors.append(f"الحقل '{field}' مطلوب")
            
            # التحقق من صحة التاريخ
            if 'activity_date' in row and not pd.isna(row['activity_date']):
                try:
                    if isinstance(row['activity_date'], str):
                        datetime.strptime(row['activity_date'], '%Y-%m-%d')
                except ValueError:
                    try:
                        datetime.strptime(row['activity_date'], '%d/%m/%Y')
                    except ValueError:
                        row_errors.append("تنسيق التاريخ غير صحيح")
            
            # التحقق من الأرقام
            numeric_fields = ['participants', 'target_participants', 'budget', 'actual_cost']
            for field in numeric_fields:
                if field in row and not pd.isna(row[field]):
                    try:
                        value = float(row[field])
                        if value < 0:
                            row_warnings.append(f"القيمة السالبة في '{field}' قد تكون غير صحيحة")
                    except (ValueError, TypeError):
                        row_errors.append(f"القيمة في '{field}' يجب أن تكون رقماً")
            
            # التحقق من القيم المحددة مسبقاً
            if 'status' in row and not pd.isna(row['status']):
                valid_statuses = ['مخطط', 'جاري', 'مكتمل', 'ملغي']
                if row['status'] not in valid_statuses:
                    row_warnings.append(f"حالة غير معروفة: {row['status']}")
            
            if 'priority' in row and not pd.isna(row['priority']):
                valid_priorities = ['عالي', 'متوسط', 'منخفض']
                if row['priority'] not in valid_priorities:
                    row_warnings.append(f"أولوية غير معروفة: {row['priority']}")
            
            if 'is_approved' in row and not pd.isna(row['is_approved']):
                valid_approvals = ['نعم', 'لا']
                if row['is_approved'] not in valid_approvals:
                    row_warnings.append(f"قيمة اعتماد غير معروفة: {row['is_approved']}")
            
            if row_errors:
                self.errors.append(f"الصف {index + 2}: {', '.join(row_errors)}")
            
            if row_warnings:
                self.warnings.append(f"الصف {index + 2}: {', '.join(row_warnings)}")
        
        return len(self.errors) == 0
    
    def get_validation_report(self):
        """الحصول على تقرير التحقق"""
        report = []
        
        if self.errors:
            report.append("الأخطاء:")
            report.extend([f"❌ {error}" for error in self.errors])
            report.append("")
        
        if self.warnings:
            report.append("التحذيرات:")
            report.extend([f"⚠️ {warning}" for warning in self.warnings])
        
        if not self.errors and not self.warnings:
            report.append("✅ جميع البيانات صحيحة")
        
        return "\n".join(report)


class ImportWorker(QThread):
    """عامل الاستيراد في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    data_loaded = pyqtSignal(object)
    import_completed = pyqtSignal(int)
    import_failed = pyqtSignal(str)
    validation_completed = pyqtSignal(object, object)  # data, validator
    
    def __init__(self, file_path, import_config, conn):
        super().__init__()
        self.file_path = file_path
        self.import_config = import_config
        self.conn = conn
        
    def run(self):
        try:
            self.status_updated.emit("بدء عملية الاستيراد...")
            self.progress_updated.emit(10)
            
            # تحديد نوع الملف
            file_extension = os.path.splitext(self.file_path)[1].lower()
            
            if file_extension == '.xlsx' or file_extension == '.xls':
                data = self.load_excel_file()
            elif file_extension == '.csv':
                data = self.load_csv_file()
            elif file_extension == '.json':
                data = self.load_json_file()
            else:
                raise ValueError(f"نوع ملف غير مدعوم: {file_extension}")
            
            self.progress_updated.emit(40)
            
            # التحقق من صحة البيانات
            self.status_updated.emit("التحقق من صحة البيانات...")
            validator = DataValidator()
            is_valid = validator.validate_activity_data(data)
            
            self.validation_completed.emit(data, validator)
            self.progress_updated.emit(60)
            
            if is_valid or self.import_config.get('ignore_errors', False):
                # استيراد البيانات
                self.status_updated.emit("استيراد البيانات...")
                imported_count = self.import_data(data)
                self.progress_updated.emit(100)
                self.import_completed.emit(imported_count)
            else:
                self.import_failed.emit("فشل التحقق من صحة البيانات")
                
        except Exception as e:
            self.import_failed.emit(str(e))
    
    def load_excel_file(self):
        """تحميل ملف Excel"""
        self.status_updated.emit("قراءة ملف Excel...")
        
        sheet_name = self.import_config.get('sheet_name', 0)
        header_row = self.import_config.get('header_row', 0)
        
        try:
            data = pd.read_excel(self.file_path, sheet_name=sheet_name, header=header_row)
        except Exception as e:
            # محاولة قراءة الورقة الأولى إذا فشل
            data = pd.read_excel(self.file_path, sheet_name=0, header=header_row)
        
        return self.clean_data(data)
    
    def load_csv_file(self):
        """تحميل ملف CSV"""
        self.status_updated.emit("قراءة ملف CSV...")
        
        # تحديد ترميز الملف
        with open(self.file_path, 'rb') as f:
            raw_data = f.read()
            encoding = chardet.detect(raw_data)['encoding']
        
        separator = self.import_config.get('separator', ',')
        
        try:
            data = pd.read_csv(self.file_path, encoding=encoding, sep=separator)
        except UnicodeDecodeError:
            # محاولة ترميزات أخرى
            for enc in ['utf-8', 'utf-8-sig', 'cp1256', 'iso-8859-1']:
                try:
                    data = pd.read_csv(self.file_path, encoding=enc, sep=separator)
                    break
                except:
                    continue
            else:
                raise ValueError("فشل في تحديد ترميز الملف")
        
        return self.clean_data(data)
    
    def load_json_file(self):
        """تحميل ملف JSON"""
        self.status_updated.emit("قراءة ملف JSON...")
        
        with open(self.file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # التعامل مع تنسيقات JSON مختلفة
        if isinstance(json_data, list):
            data = pd.DataFrame(json_data)
        elif isinstance(json_data, dict):
            if 'activities' in json_data:
                data = pd.DataFrame(json_data['activities'])
            elif 'data' in json_data:
                data = pd.DataFrame(json_data['data'])
            else:
                # محاولة تحويل القاموس إلى DataFrame
                data = pd.DataFrame([json_data])
        else:
            raise ValueError("تنسيق JSON غير مدعوم")
        
        return self.clean_data(data)
    
    def clean_data(self, data):
        """تنظيف البيانات"""
        # إزالة الصفوف الفارغة
        data = data.dropna(how='all')
        
        # تنظيف أسماء الأعمدة
        data.columns = data.columns.str.strip()
        
        # تحويل أسماء الأعمدة الإنجليزية إلى العربية
        column_mapping = {
            'person': 'person',
            'name': 'person',
            'activity_name': 'activity_name',
            'activity': 'activity_name',
            'category': 'parent_category',
            'parent_category': 'parent_category',
            'sub_category': 'sub_category',
            'subcategory': 'sub_category',
            'governorate': 'governorate',
            'district': 'district',
            'participants': 'participants',
            'date': 'activity_date',
            'activity_date': 'activity_date',
            'budget': 'budget',
            'cost': 'actual_cost',
            'status': 'status',
            'priority': 'priority',
            'approved': 'is_approved'
        }
        
        # إعادة تسمية الأعمدة
        for old_name, new_name in column_mapping.items():
            if old_name in data.columns:
                data = data.rename(columns={old_name: new_name})
        
        return data
    
    def import_data(self, data):
        """استيراد البيانات إلى قاعدة البيانات"""
        cursor = self.conn.cursor()
        imported_count = 0
        
        for index, row in data.iterrows():
            try:
                # تحضير البيانات
                activity_data = {}
                
                # الحقول الأساسية
                basic_fields = [
                    'person', 'activity_name', 'parent_category', 'sub_category',
                    'is_approved', 'governorate', 'district', 'sub_district', 'village',
                    'participants', 'target_participants', 'male_participants', 'female_participants',
                    'budget', 'actual_cost', 'results', 'recommendations', 'notes',
                    'activity_date', 'start_date', 'end_date', 'status', 'priority',
                    'location_coordinates', 'contact_person', 'contact_phone'
                ]
                
                for field in basic_fields:
                    if field in row and not pd.isna(row[field]):
                        activity_data[field] = row[field]
                    else:
                        # قيم افتراضية
                        if field == 'is_approved':
                            activity_data[field] = 'لا'
                        elif field == 'status':
                            activity_data[field] = 'مخطط'
                        elif field == 'priority':
                            activity_data[field] = 'متوسط'
                        elif field in ['participants', 'target_participants', 'male_participants', 'female_participants']:
                            activity_data[field] = 0
                        elif field in ['budget', 'actual_cost']:
                            activity_data[field] = 0.0
                        else:
                            activity_data[field] = ''
                
                # تحويل التاريخ إلى التنسيق الصحيح
                if 'activity_date' in activity_data and activity_data['activity_date']:
                    try:
                        if isinstance(activity_data['activity_date'], str):
                            # محاولة تنسيقات مختلفة
                            for date_format in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y/%m/%d']:
                                try:
                                    date_obj = datetime.strptime(activity_data['activity_date'], date_format)
                                    activity_data['activity_date'] = date_obj.strftime('%Y-%m-%d')
                                    break
                                except ValueError:
                                    continue
                    except:
                        activity_data['activity_date'] = datetime.now().strftime('%Y-%m-%d')
                
                # إدراج البيانات
                fields = list(activity_data.keys())
                placeholders = ', '.join(['?' for _ in fields])
                values = list(activity_data.values())
                
                query = f"INSERT INTO activities ({', '.join(fields)}) VALUES ({placeholders})"
                cursor.execute(query, values)
                
                imported_count += 1
                
                # تحديث شريط التقدم
                progress = 60 + (index / len(data)) * 40
                self.progress_updated.emit(int(progress))
                
            except Exception as e:
                print(f"خطأ في استيراد الصف {index + 1}: {e}")
                continue
        
        self.conn.commit()
        return imported_count


class ImportWidget(QWidget):
    """واجهة الاستيراد الذكية"""

    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.import_worker = None
        self.current_data = None
        self.current_validator = None
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("📥 نظام الاستيراد الذكي")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #673AB7, stop:1 #512DA8);
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تبويبات الاستيراد
        tab_widget = QTabWidget()

        # تبويب الاستيراد السريع
        quick_import_tab = self.create_quick_import_tab()
        tab_widget.addTab(quick_import_tab, "الاستيراد السريع")

        # تبويب الاستيراد المتقدم
        advanced_import_tab = self.create_advanced_import_tab()
        tab_widget.addTab(advanced_import_tab, "الاستيراد المتقدم")

        # تبويب معاينة البيانات
        preview_tab = self.create_preview_tab()
        tab_widget.addTab(preview_tab, "معاينة البيانات")

        # تبويب التحقق من الصحة
        validation_tab = self.create_validation_tab()
        tab_widget.addTab(validation_tab, "التحقق من الصحة")

        layout.addWidget(tab_widget)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def create_quick_import_tab(self):
        """إنشاء تبويب الاستيراد السريع"""
        widget = QWidget()
        layout = QVBoxLayout()

        # منطقة اختيار الملف
        file_group = QGroupBox("اختيار الملف")
        file_layout = QVBoxLayout()

        # زر اختيار الملف
        self.file_path_label = QLabel("لم يتم اختيار ملف")
        self.file_path_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                background-color: #f9f9f9;
                color: #666;
            }
        """)
        file_layout.addWidget(self.file_path_label)

        # أزرار اختيار الملف
        file_buttons = QHBoxLayout()

        select_excel_btn = QPushButton("📊 اختيار ملف Excel")
        select_excel_btn.clicked.connect(lambda: self.select_file("Excel"))
        file_buttons.addWidget(select_excel_btn)

        select_csv_btn = QPushButton("📋 اختيار ملف CSV")
        select_csv_btn.clicked.connect(lambda: self.select_file("CSV"))
        file_buttons.addWidget(select_csv_btn)

        select_json_btn = QPushButton("🔗 اختيار ملف JSON")
        select_json_btn.clicked.connect(lambda: self.select_file("JSON"))
        file_buttons.addWidget(select_json_btn)

        file_layout.addLayout(file_buttons)
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # خيارات الاستيراد السريع
        options_group = QGroupBox("خيارات الاستيراد")
        options_layout = QFormLayout()

        self.skip_duplicates = QCheckBox("تجاهل السجلات المكررة")
        self.skip_duplicates.setChecked(True)
        options_layout.addRow("", self.skip_duplicates)

        self.validate_data = QCheckBox("التحقق من صحة البيانات")
        self.validate_data.setChecked(True)
        options_layout.addRow("", self.validate_data)

        self.backup_before_import = QCheckBox("إنشاء نسخة احتياطية قبل الاستيراد")
        self.backup_before_import.setChecked(True)
        options_layout.addRow("", self.backup_before_import)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # زر الاستيراد السريع
        import_btn = QPushButton("🚀 بدء الاستيراد السريع")
        import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #673AB7, stop:1 #512DA8);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7E57C2, stop:1 #673AB7);
            }
        """)
        import_btn.clicked.connect(self.quick_import)
        layout.addWidget(import_btn)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_advanced_import_tab(self):
        """إنشاء تبويب الاستيراد المتقدم"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إعدادات الملف
        file_settings_group = QGroupBox("إعدادات الملف")
        file_settings_layout = QFormLayout()

        # ورقة Excel
        self.sheet_name_combo = QComboBox()
        self.sheet_name_combo.setEditable(True)
        file_settings_layout.addRow("ورقة Excel:", self.sheet_name_combo)

        # صف الرؤوس
        self.header_row_spin = QSpinBox()
        self.header_row_spin.setMinimum(0)
        self.header_row_spin.setMaximum(100)
        self.header_row_spin.setValue(0)
        file_settings_layout.addRow("صف الرؤوس:", self.header_row_spin)

        # فاصل CSV
        self.csv_separator = QComboBox()
        self.csv_separator.addItems([",", ";", "\t", "|"])
        self.csv_separator.setEditable(True)
        file_settings_layout.addRow("فاصل CSV:", self.csv_separator)

        # ترميز الملف
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["utf-8", "utf-8-sig", "cp1256", "iso-8859-1", "auto-detect"])
        file_settings_layout.addRow("ترميز الملف:", self.encoding_combo)

        file_settings_group.setLayout(file_settings_layout)
        layout.addWidget(file_settings_group)

        # تطابق الأعمدة
        mapping_group = QGroupBox("تطابق الأعمدة")
        mapping_layout = QVBoxLayout()

        self.column_mapping_table = QTableWidget()
        self.column_mapping_table.setColumnCount(3)
        self.column_mapping_table.setHorizontalHeaderLabels(["عمود الملف", "حقل قاعدة البيانات", "مطلوب"])
        self.column_mapping_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        mapping_layout.addWidget(self.column_mapping_table)

        # أزرار تطابق الأعمدة
        mapping_buttons = QHBoxLayout()

        auto_map_btn = QPushButton("🔄 تطابق تلقائي")
        auto_map_btn.clicked.connect(self.auto_map_columns)
        mapping_buttons.addWidget(auto_map_btn)

        clear_map_btn = QPushButton("🗑️ مسح التطابق")
        clear_map_btn.clicked.connect(self.clear_column_mapping)
        mapping_buttons.addWidget(clear_map_btn)

        mapping_layout.addLayout(mapping_buttons)
        mapping_group.setLayout(mapping_layout)
        layout.addWidget(mapping_group)

        # خيارات متقدمة
        advanced_options_group = QGroupBox("خيارات متقدمة")
        advanced_options_layout = QFormLayout()

        self.ignore_errors = QCheckBox("تجاهل الأخطاء والمتابعة")
        advanced_options_layout.addRow("", self.ignore_errors)

        self.update_existing = QCheckBox("تحديث السجلات الموجودة")
        advanced_options_layout.addRow("", self.update_existing)

        self.batch_size = QSpinBox()
        self.batch_size.setMinimum(1)
        self.batch_size.setMaximum(10000)
        self.batch_size.setValue(1000)
        advanced_options_layout.addRow("حجم الدفعة:", self.batch_size)

        advanced_options_group.setLayout(advanced_options_layout)
        layout.addWidget(advanced_options_group)

        # زر الاستيراد المتقدم
        advanced_import_btn = QPushButton("⚡ بدء الاستيراد المتقدم")
        advanced_import_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF5722, stop:1 #E64A19);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF6F43, stop:1 #FF5722);
            }
        """)
        advanced_import_btn.clicked.connect(self.advanced_import)
        layout.addWidget(advanced_import_btn)

        widget.setLayout(layout)
        return widget

    def create_preview_tab(self):
        """إنشاء تبويب معاينة البيانات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # معلومات الملف
        info_group = QGroupBox("معلومات الملف")
        info_layout = QFormLayout()

        self.file_info_label = QLabel("لم يتم تحميل ملف")
        info_layout.addRow("الملف:", self.file_info_label)

        self.rows_count_label = QLabel("0")
        info_layout.addRow("عدد الصفوف:", self.rows_count_label)

        self.columns_count_label = QLabel("0")
        info_layout.addRow("عدد الأعمدة:", self.columns_count_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # جدول معاينة البيانات
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        layout.addWidget(self.preview_table)

        # أزرار التحكم
        control_buttons = QHBoxLayout()

        load_preview_btn = QPushButton("🔍 تحميل معاينة")
        load_preview_btn.clicked.connect(self.load_preview)
        control_buttons.addWidget(load_preview_btn)

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_preview)
        control_buttons.addWidget(refresh_btn)

        control_buttons.addStretch()
        layout.addLayout(control_buttons)

        widget.setLayout(layout)
        return widget

    def create_validation_tab(self):
        """إنشاء تبويب التحقق من الصحة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # نتائج التحقق
        validation_group = QGroupBox("نتائج التحقق من الصحة")
        validation_layout = QVBoxLayout()

        self.validation_report = QTextEdit()
        self.validation_report.setReadOnly(True)
        self.validation_report.setMaximumHeight(300)
        validation_layout.addWidget(self.validation_report)

        validation_group.setLayout(validation_layout)
        layout.addWidget(validation_group)

        # إحصائيات التحقق
        stats_group = QGroupBox("إحصائيات التحقق")
        stats_layout = QFormLayout()

        self.valid_rows_label = QLabel("0")
        stats_layout.addRow("الصفوف الصحيحة:", self.valid_rows_label)

        self.error_rows_label = QLabel("0")
        stats_layout.addRow("الصفوف بها أخطاء:", self.error_rows_label)

        self.warning_rows_label = QLabel("0")
        stats_layout.addRow("الصفوف بها تحذيرات:", self.warning_rows_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # أزرار التحقق
        validation_buttons = QHBoxLayout()

        validate_btn = QPushButton("✅ تشغيل التحقق")
        validate_btn.clicked.connect(self.run_validation)
        validation_buttons.addWidget(validate_btn)

        export_report_btn = QPushButton("📄 تصدير التقرير")
        export_report_btn.clicked.connect(self.export_validation_report)
        validation_buttons.addWidget(export_report_btn)

        validation_buttons.addStretch()
        layout.addLayout(validation_buttons)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def select_file(self, file_type):
        """اختيار ملف للاستيراد"""
        filters = {
            "Excel": "Excel Files (*.xlsx *.xls)",
            "CSV": "CSV Files (*.csv)",
            "JSON": "JSON Files (*.json)"
        }

        file_path, _ = QFileDialog.getOpenFileName(
            self, f"اختيار ملف {file_type}",
            "", filters.get(file_type, "All Files (*)")
        )

        if file_path:
            self.file_path_label.setText(file_path)
            self.file_path_label.setStyleSheet("""
                QLabel {
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    padding: 20px;
                    text-align: center;
                    background-color: #e8f5e8;
                    color: #2e7d32;
                    font-weight: bold;
                }
            """)

            # تحديث معلومات الملف
            self.file_info_label.setText(os.path.basename(file_path))

            # تحميل أوراق Excel إذا كان ملف Excel
            if file_path.endswith(('.xlsx', '.xls')):
                self.load_excel_sheets(file_path)

    def load_excel_sheets(self, file_path):
        """تحميل أوراق Excel"""
        try:
            excel_file = pd.ExcelFile(file_path)
            self.sheet_name_combo.clear()
            self.sheet_name_combo.addItems(excel_file.sheet_names)
        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في قراءة أوراق Excel: {str(e)}")

    def quick_import(self):
        """الاستيراد السريع"""
        file_path = self.file_path_label.text()

        if file_path == "لم يتم اختيار ملف":
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف أولاً")
            return

        config = {
            'skip_duplicates': self.skip_duplicates.isChecked(),
            'validate_data': self.validate_data.isChecked(),
            'backup_before_import': self.backup_before_import.isChecked(),
            'ignore_errors': False
        }

        self.start_import(file_path, config)

    def advanced_import(self):
        """الاستيراد المتقدم"""
        file_path = self.file_path_label.text()

        if file_path == "لم يتم اختيار ملف":
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف أولاً")
            return

        config = {
            'sheet_name': self.sheet_name_combo.currentText(),
            'header_row': self.header_row_spin.value(),
            'separator': self.csv_separator.currentText(),
            'encoding': self.encoding_combo.currentText(),
            'ignore_errors': self.ignore_errors.isChecked(),
            'update_existing': self.update_existing.isChecked(),
            'batch_size': self.batch_size.value()
        }

        self.start_import(file_path, config)

    def start_import(self, file_path, config):
        """بدء عملية الاستيراد"""
        try:
            # إنشاء نسخة احتياطية إذا كان مطلوباً
            if config.get('backup_before_import', False):
                self.create_backup()

            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # إنشاء عامل الاستيراد
            self.import_worker = ImportWorker(file_path, config, self.conn)
            self.import_worker.progress_updated.connect(self.progress_bar.setValue)
            self.import_worker.status_updated.connect(self.status_label.setText)
            self.import_worker.validation_completed.connect(self.validation_completed)
            self.import_worker.import_completed.connect(self.import_completed)
            self.import_worker.import_failed.connect(self.import_failed)

            self.import_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء الاستيراد: {str(e)}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            backup_filename = f"backup_activities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

            # نسخ قاعدة البيانات
            cursor = self.conn.cursor()
            cursor.execute("VACUUM INTO ?", (backup_filename,))

            self.status_label.setText(f"تم إنشاء نسخة احتياطية: {backup_filename}")

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def load_preview(self):
        """تحميل معاينة البيانات"""
        file_path = self.file_path_label.text()

        if file_path == "لم يتم اختيار ملف":
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف أولاً")
            return

        try:
            # تحديد نوع الملف وتحميل البيانات
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension in ['.xlsx', '.xls']:
                sheet_name = self.sheet_name_combo.currentText() or 0
                data = pd.read_excel(file_path, sheet_name=sheet_name, nrows=100)
            elif file_extension == '.csv':
                separator = self.csv_separator.currentText()
                data = pd.read_csv(file_path, sep=separator, nrows=100)
            elif file_extension == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                if isinstance(json_data, list):
                    data = pd.DataFrame(json_data[:100])
                else:
                    data = pd.DataFrame([json_data])
            else:
                QMessageBox.warning(self, "تحذير", "نوع ملف غير مدعوم")
                return

            # تحديث معلومات الملف
            self.rows_count_label.setText(str(len(data)))
            self.columns_count_label.setText(str(len(data.columns)))

            # عرض البيانات في الجدول
            self.preview_table.setRowCount(len(data))
            self.preview_table.setColumnCount(len(data.columns))
            self.preview_table.setHorizontalHeaderLabels(data.columns.tolist())

            for row in range(len(data)):
                for col in range(len(data.columns)):
                    value = str(data.iloc[row, col])
                    item = QTableWidgetItem(value)
                    self.preview_table.setItem(row, col, item)

            self.preview_table.resizeColumnsToContents()
            self.current_data = data

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المعاينة: {str(e)}")

    def refresh_preview(self):
        """تحديث المعاينة"""
        self.load_preview()

    def auto_map_columns(self):
        """تطابق الأعمدة تلقائياً"""
        if self.current_data is None:
            QMessageBox.warning(self, "تحذير", "يرجى تحميل معاينة البيانات أولاً")
            return

        # قائمة الحقول المتاحة في قاعدة البيانات
        db_fields = [
            'person', 'activity_name', 'parent_category', 'sub_category',
            'governorate', 'district', 'sub_district', 'village',
            'participants', 'target_participants', 'budget', 'actual_cost',
            'status', 'priority', 'activity_date', 'results', 'notes'
        ]

        # تطابق تلقائي بناءً على أسماء الأعمدة
        file_columns = self.current_data.columns.tolist()

        self.column_mapping_table.setRowCount(len(file_columns))

        for i, file_col in enumerate(file_columns):
            # عمود الملف
            file_item = QTableWidgetItem(file_col)
            file_item.setFlags(file_item.flags() & ~Qt.ItemIsEditable)
            self.column_mapping_table.setItem(i, 0, file_item)

            # حقل قاعدة البيانات
            db_combo = QComboBox()
            db_combo.addItem("")  # خيار فارغ
            db_combo.addItems(db_fields)

            # محاولة التطابق التلقائي
            file_col_lower = file_col.lower().strip()
            for db_field in db_fields:
                if (file_col_lower == db_field or
                    file_col_lower in db_field or
                    db_field in file_col_lower):
                    db_combo.setCurrentText(db_field)
                    break

            self.column_mapping_table.setCellWidget(i, 1, db_combo)

            # مطلوب
            required_fields = ['person', 'activity_name', 'activity_date']
            required_item = QTableWidgetItem("نعم" if db_combo.currentText() in required_fields else "لا")
            required_item.setFlags(required_item.flags() & ~Qt.ItemIsEditable)
            self.column_mapping_table.setItem(i, 2, required_item)

    def clear_column_mapping(self):
        """مسح تطابق الأعمدة"""
        self.column_mapping_table.setRowCount(0)

    def run_validation(self):
        """تشغيل التحقق من صحة البيانات"""
        if self.current_data is None:
            QMessageBox.warning(self, "تحذير", "يرجى تحميل معاينة البيانات أولاً")
            return

        validator = DataValidator()
        validator.validate_activity_data(self.current_data)

        # عرض تقرير التحقق
        report = validator.get_validation_report()
        self.validation_report.setText(report)

        # تحديث الإحصائيات
        error_count = len(validator.errors)
        warning_count = len(validator.warnings)
        valid_count = len(self.current_data) - error_count

        self.valid_rows_label.setText(str(valid_count))
        self.error_rows_label.setText(str(error_count))
        self.warning_rows_label.setText(str(warning_count))

        self.current_validator = validator

    def export_validation_report(self):
        """تصدير تقرير التحقق"""
        if self.current_validator is None:
            QMessageBox.warning(self, "تحذير", "يرجى تشغيل التحقق أولاً")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "حفظ تقرير التحقق",
            f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_validator.get_validation_report())
                QMessageBox.information(self, "نجح", "تم تصدير تقرير التحقق بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير: {str(e)}")

    def validation_completed(self, data, validator):
        """اكتمال التحقق من الصحة"""
        self.current_data = data
        self.current_validator = validator

        # عرض تقرير التحقق
        report = validator.get_validation_report()
        self.validation_report.setText(report)

        # تحديث الإحصائيات
        error_count = len(validator.errors)
        warning_count = len(validator.warnings)
        valid_count = len(data) - error_count

        self.valid_rows_label.setText(str(valid_count))
        self.error_rows_label.setText(str(error_count))
        self.warning_rows_label.setText(str(warning_count))

    def import_completed(self, imported_count):
        """اكتمال الاستيراد"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"تم استيراد {imported_count} سجل بنجاح")
        QMessageBox.information(self, "نجح", f"تم استيراد {imported_count} سجل بنجاح")

    def import_failed(self, error):
        """فشل الاستيراد"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل الاستيراد")
        QMessageBox.critical(self, "خطأ", f"فشل في الاستيراد:\n{error}")
