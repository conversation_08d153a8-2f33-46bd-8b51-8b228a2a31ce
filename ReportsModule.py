"""
وحدة التقارير الإحصائية المتقدمة
تتضمن إنتاج تقارير شاملة مع رسوم بيانية احترافية
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QDateEdit, QSpinBox, QTextEdit, QTabWidget,
    QGroupBox, QFormLayout, QCheckBox, QProgressBar, QMessageBox,
    QFileDialog, QTableWidget, QTableWidgetItem, QHeader<PERSON>iew,
    QSplitter, Q<PERSON>rame, QScrollArea
)
from PyQt5.QtCore import QDate, Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
import matplotlib
matplotlib.use('Qt5Agg')

# تعيين الخط العربي لـ matplotlib
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ReportGenerator(QThread):
    """مولد التقارير في خيط منفصل لتجنب تجميد الواجهة"""
    
    progress_updated = pyqtSignal(int)
    report_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, report_type, filters, conn):
        super().__init__()
        self.report_type = report_type
        self.filters = filters
        self.conn = conn
        
    def run(self):
        try:
            self.progress_updated.emit(10)
            
            if self.report_type == "summary":
                report_data = self.generate_summary_report()
            elif self.report_type == "detailed":
                report_data = self.generate_detailed_report()
            elif self.report_type == "performance":
                report_data = self.generate_performance_report()
            elif self.report_type == "geographic":
                report_data = self.generate_geographic_report()
            elif self.report_type == "financial":
                report_data = self.generate_financial_report()
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")
            
            self.progress_updated.emit(100)
            self.report_ready.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def generate_summary_report(self):
        """إنتاج تقرير ملخص شامل"""
        cursor = self.conn.cursor()
        
        # إحصائيات عامة
        self.progress_updated.emit(20)
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_activities = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
        completed_activities = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(participants) FROM activities WHERE participants IS NOT NULL")
        total_participants = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(budget) FROM activities WHERE budget IS NOT NULL")
        total_budget = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(actual_cost) FROM activities WHERE actual_cost IS NOT NULL")
        total_cost = cursor.fetchone()[0] or 0
        
        # إحصائيات حسب التصنيف
        self.progress_updated.emit(40)
        cursor.execute("""
            SELECT parent_category, COUNT(*), SUM(participants), SUM(budget), SUM(actual_cost)
            FROM activities 
            GROUP BY parent_category 
            ORDER BY COUNT(*) DESC
        """)
        category_stats = cursor.fetchall()
        
        # إحصائيات حسب المحافظة
        self.progress_updated.emit(60)
        cursor.execute("""
            SELECT governorate, COUNT(*), SUM(participants), SUM(budget)
            FROM activities 
            GROUP BY governorate 
            ORDER BY COUNT(*) DESC
        """)
        location_stats = cursor.fetchall()
        
        # إحصائيات حسب الشهر
        self.progress_updated.emit(80)
        cursor.execute("""
            SELECT strftime('%Y-%m', activity_date) as month, COUNT(*), SUM(participants)
            FROM activities 
            GROUP BY strftime('%Y-%m', activity_date)
            ORDER BY month DESC
            LIMIT 12
        """)
        monthly_stats = cursor.fetchall()
        
        return {
            'type': 'summary',
            'general_stats': {
                'total_activities': total_activities,
                'completed_activities': completed_activities,
                'completion_rate': (completed_activities / total_activities * 100) if total_activities > 0 else 0,
                'total_participants': total_participants,
                'total_budget': total_budget,
                'total_cost': total_cost,
                'budget_efficiency': ((total_budget - total_cost) / total_budget * 100) if total_budget > 0 else 0
            },
            'category_stats': category_stats,
            'location_stats': location_stats,
            'monthly_stats': monthly_stats,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def generate_detailed_report(self):
        """إنتاج تقرير مفصل"""
        cursor = self.conn.cursor()
        
        # بناء استعلام مفصل مع الفلاتر
        where_conditions = []
        params = []
        
        if self.filters.get('start_date'):
            where_conditions.append("activity_date >= ?")
            params.append(self.filters['start_date'])
            
        if self.filters.get('end_date'):
            where_conditions.append("activity_date <= ?")
            params.append(self.filters['end_date'])
            
        if self.filters.get('category'):
            where_conditions.append("parent_category = ?")
            params.append(self.filters['category'])
            
        if self.filters.get('governorate'):
            where_conditions.append("governorate = ?")
            params.append(self.filters['governorate'])
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        self.progress_updated.emit(30)
        
        query = f"""
            SELECT id, person, activity_name, parent_category, sub_category,
                   governorate, district, participants, target_participants,
                   budget, actual_cost, status, priority, activity_date,
                   start_date, end_date, results, recommendations
            FROM activities
            {where_clause}
            ORDER BY activity_date DESC
        """
        
        cursor.execute(query, params)
        activities = cursor.fetchall()
        
        self.progress_updated.emit(70)
        
        # تحليل البيانات المفصلة
        df = pd.DataFrame(activities, columns=[
            'id', 'person', 'activity_name', 'parent_category', 'sub_category',
            'governorate', 'district', 'participants', 'target_participants',
            'budget', 'actual_cost', 'status', 'priority', 'activity_date',
            'start_date', 'end_date', 'results', 'recommendations'
        ])
        
        # إحصائيات متقدمة
        analysis = {
            'total_records': len(df),
            'avg_participants': df['participants'].mean() if not df.empty else 0,
            'avg_budget': df['budget'].mean() if not df.empty else 0,
            'status_distribution': df['status'].value_counts().to_dict() if not df.empty else {},
            'priority_distribution': df['priority'].value_counts().to_dict() if not df.empty else {},
            'category_performance': df.groupby('parent_category').agg({
                'participants': 'sum',
                'budget': 'sum',
                'actual_cost': 'sum'
            }).to_dict() if not df.empty else {}
        }
        
        return {
            'type': 'detailed',
            'activities': activities,
            'analysis': analysis,
            'filters_applied': self.filters,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def generate_performance_report(self):
        """إنتاج تقرير الأداء"""
        cursor = self.conn.cursor()
        
        # مؤشرات الأداء الرئيسية
        self.progress_updated.emit(25)
        
        # معدل إنجاز الأنشطة
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) * 100.0 / COUNT(*) as completion_rate,
                COUNT(CASE WHEN participants >= target_participants THEN 1 END) * 100.0 / COUNT(*) as target_achievement_rate,
                AVG(CASE WHEN actual_cost > 0 AND budget > 0 THEN (budget - actual_cost) * 100.0 / budget END) as budget_efficiency
            FROM activities
            WHERE target_participants > 0
        """)
        kpis = cursor.fetchone()
        
        self.progress_updated.emit(50)
        
        # أداء الأشخاص
        cursor.execute("""
            SELECT person, 
                   COUNT(*) as total_activities,
                   COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) as completed,
                   SUM(participants) as total_participants,
                   AVG(participants) as avg_participants
            FROM activities
            GROUP BY person
            ORDER BY completed DESC, total_participants DESC
            LIMIT 10
        """)
        person_performance = cursor.fetchall()
        
        self.progress_updated.emit(75)
        
        # أداء التصنيفات
        cursor.execute("""
            SELECT parent_category,
                   COUNT(*) as total_activities,
                   COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) as completed,
                   SUM(participants) as total_participants,
                   SUM(budget) as total_budget,
                   SUM(actual_cost) as total_cost
            FROM activities
            GROUP BY parent_category
            ORDER BY completed DESC
        """)
        category_performance = cursor.fetchall()
        
        return {
            'type': 'performance',
            'kpis': {
                'completion_rate': kpis[0] if kpis[0] else 0,
                'target_achievement_rate': kpis[1] if kpis[1] else 0,
                'budget_efficiency': kpis[2] if kpis[2] else 0
            },
            'person_performance': person_performance,
            'category_performance': category_performance,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def generate_geographic_report(self):
        """إنتاج تقرير جغرافي"""
        cursor = self.conn.cursor()
        
        self.progress_updated.emit(30)
        
        # توزيع الأنشطة حسب المحافظات
        cursor.execute("""
            SELECT governorate, district, 
                   COUNT(*) as activities_count,
                   SUM(participants) as total_participants,
                   SUM(budget) as total_budget
            FROM activities
            WHERE governorate IS NOT NULL AND governorate != ''
            GROUP BY governorate, district
            ORDER BY activities_count DESC
        """)
        geographic_distribution = cursor.fetchall()
        
        self.progress_updated.emit(60)
        
        # كثافة الأنشطة حسب المنطقة
        cursor.execute("""
            SELECT governorate,
                   COUNT(*) as activities_count,
                   COUNT(DISTINCT person) as unique_persons,
                   SUM(participants) as total_participants
            FROM activities
            WHERE governorate IS NOT NULL AND governorate != ''
            GROUP BY governorate
            ORDER BY activities_count DESC
        """)
        governorate_summary = cursor.fetchall()
        
        return {
            'type': 'geographic',
            'geographic_distribution': geographic_distribution,
            'governorate_summary': governorate_summary,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def generate_financial_report(self):
        """إنتاج تقرير مالي"""
        cursor = self.conn.cursor()
        
        self.progress_updated.emit(25)
        
        # الملخص المالي العام
        cursor.execute("""
            SELECT 
                SUM(budget) as total_budget,
                SUM(actual_cost) as total_cost,
                SUM(budget - actual_cost) as savings,
                COUNT(*) as total_activities,
                AVG(budget) as avg_budget_per_activity,
                AVG(actual_cost) as avg_cost_per_activity
            FROM activities
            WHERE budget > 0 OR actual_cost > 0
        """)
        financial_summary = cursor.fetchone()
        
        self.progress_updated.emit(50)
        
        # التوزيع المالي حسب التصنيف
        cursor.execute("""
            SELECT parent_category,
                   SUM(budget) as category_budget,
                   SUM(actual_cost) as category_cost,
                   COUNT(*) as activities_count
            FROM activities
            WHERE budget > 0 OR actual_cost > 0
            GROUP BY parent_category
            ORDER BY category_budget DESC
        """)
        category_financial = cursor.fetchall()
        
        self.progress_updated.emit(75)
        
        # التوزيع المالي حسب الشهر
        cursor.execute("""
            SELECT strftime('%Y-%m', activity_date) as month,
                   SUM(budget) as monthly_budget,
                   SUM(actual_cost) as monthly_cost,
                   COUNT(*) as activities_count
            FROM activities
            WHERE budget > 0 OR actual_cost > 0
            GROUP BY strftime('%Y-%m', activity_date)
            ORDER BY month DESC
            LIMIT 12
        """)
        monthly_financial = cursor.fetchall()
        
        return {
            'type': 'financial',
            'financial_summary': {
                'total_budget': financial_summary[0] or 0,
                'total_cost': financial_summary[1] or 0,
                'savings': financial_summary[2] or 0,
                'total_activities': financial_summary[3] or 0,
                'avg_budget_per_activity': financial_summary[4] or 0,
                'avg_cost_per_activity': financial_summary[5] or 0
            },
            'category_financial': category_financial,
            'monthly_financial': monthly_financial,
            'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }


class ReportVisualization:
    """فئة لإنشاء الرسوم البيانية للتقارير"""

    def __init__(self):
        self.colors = ['#4e79a7', '#f28e2c', '#e15759', '#76b7b2', '#59a14f',
                      '#edc949', '#af7aa1', '#ff9da7', '#9c755f', '#bab0ab']

    def create_summary_charts(self, report_data):
        """إنشاء رسوم بيانية للتقرير الملخص"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('تقرير ملخص الأنشطة', fontsize=16, fontweight='bold')

        # رسم بياني دائري للتصنيفات
        if report_data['category_stats']:
            categories = [row[0] for row in report_data['category_stats']]
            counts = [row[1] for row in report_data['category_stats']]

            ax1.pie(counts, labels=categories, autopct='%1.1f%%', colors=self.colors)
            ax1.set_title('توزيع الأنشطة حسب التصنيف')

        # رسم بياني شريطي للمحافظات
        if report_data['location_stats']:
            locations = [row[0] for row in report_data['location_stats'][:10]]
            counts = [row[1] for row in report_data['location_stats'][:10]]

            ax2.bar(range(len(locations)), counts, color=self.colors[0])
            ax2.set_xticks(range(len(locations)))
            ax2.set_xticklabels(locations, rotation=45, ha='right')
            ax2.set_title('عدد الأنشطة حسب المحافظة')
            ax2.set_ylabel('عدد الأنشطة')

        # رسم بياني خطي للاتجاه الشهري
        if report_data['monthly_stats']:
            months = [row[0] for row in reversed(report_data['monthly_stats'])]
            counts = [row[1] for row in reversed(report_data['monthly_stats'])]

            ax3.plot(months, counts, marker='o', color=self.colors[2], linewidth=2)
            ax3.set_title('اتجاه الأنشطة الشهرية')
            ax3.set_ylabel('عدد الأنشطة')
            ax3.tick_params(axis='x', rotation=45)

        # مؤشرات الأداء الرئيسية
        general_stats = report_data['general_stats']
        kpis = [
            ('إجمالي الأنشطة', general_stats['total_activities']),
            ('الأنشطة المكتملة', general_stats['completed_activities']),
            ('إجمالي المشاركين', general_stats['total_participants']),
            ('معدل الإنجاز %', f"{general_stats['completion_rate']:.1f}")
        ]

        ax4.axis('off')
        for i, (label, value) in enumerate(kpis):
            ax4.text(0.1, 0.8 - i*0.2, f"{label}: {value}",
                    fontsize=12, fontweight='bold',
                    transform=ax4.transAxes)
        ax4.set_title('مؤشرات الأداء الرئيسية')

        plt.tight_layout()
        return fig

    def create_performance_charts(self, report_data):
        """إنشاء رسوم بيانية لتقرير الأداء"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('تقرير أداء الأنشطة', fontsize=16, fontweight='bold')

        # مؤشرات الأداء الرئيسية
        kpis = report_data['kpis']
        kpi_names = ['معدل الإنجاز', 'تحقيق الأهداف', 'كفاءة الميزانية']
        kpi_values = [kpis['completion_rate'], kpis['target_achievement_rate'], kpis['budget_efficiency']]

        bars = ax1.bar(kpi_names, kpi_values, color=self.colors[:3])
        ax1.set_title('مؤشرات الأداء الرئيسية (%)')
        ax1.set_ylabel('النسبة المئوية')

        # إضافة قيم على الأشرطة
        for bar, value in zip(bars, kpi_values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{value:.1f}%', ha='center', va='bottom')

        # أداء الأشخاص
        if report_data['person_performance']:
            persons = [row[0] for row in report_data['person_performance'][:8]]
            completed = [row[2] for row in report_data['person_performance'][:8]]

            ax2.barh(range(len(persons)), completed, color=self.colors[1])
            ax2.set_yticks(range(len(persons)))
            ax2.set_yticklabels(persons)
            ax2.set_title('أداء المنفذين (الأنشطة المكتملة)')
            ax2.set_xlabel('عدد الأنشطة المكتملة')

        # أداء التصنيفات
        if report_data['category_performance']:
            categories = [row[0] for row in report_data['category_performance']]
            total_activities = [row[1] for row in report_data['category_performance']]
            completed = [row[2] for row in report_data['category_performance']]

            x = np.arange(len(categories))
            width = 0.35

            ax3.bar(x - width/2, total_activities, width, label='إجمالي الأنشطة', color=self.colors[0])
            ax3.bar(x + width/2, completed, width, label='الأنشطة المكتملة', color=self.colors[2])

            ax3.set_xlabel('التصنيفات')
            ax3.set_ylabel('عدد الأنشطة')
            ax3.set_title('أداء التصنيفات')
            ax3.set_xticks(x)
            ax3.set_xticklabels(categories, rotation=45, ha='right')
            ax3.legend()

        # رسم بياني دائري لتوزيع المشاركين حسب التصنيف
        if report_data['category_performance']:
            categories = [row[0] for row in report_data['category_performance']]
            participants = [row[3] for row in report_data['category_performance']]

            ax4.pie(participants, labels=categories, autopct='%1.1f%%', colors=self.colors)
            ax4.set_title('توزيع المشاركين حسب التصنيف')

        plt.tight_layout()
        return fig

    def create_financial_charts(self, report_data):
        """إنشاء رسوم بيانية للتقرير المالي"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('التقرير المالي', fontsize=16, fontweight='bold')

        # الملخص المالي العام
        financial_summary = report_data['financial_summary']
        categories = ['الميزانية المخططة', 'التكلفة الفعلية', 'الوفورات']
        values = [
            financial_summary['total_budget'],
            financial_summary['total_cost'],
            financial_summary['savings']
        ]

        bars = ax1.bar(categories, values, color=self.colors[:3])
        ax1.set_title('الملخص المالي العام')
        ax1.set_ylabel('المبلغ (ريال)')

        # إضافة قيم على الأشرطة
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                    f'{value:,.0f}', ha='center', va='bottom')

        # التوزيع المالي حسب التصنيف
        if report_data['category_financial']:
            categories = [row[0] for row in report_data['category_financial']]
            budgets = [row[1] for row in report_data['category_financial']]
            costs = [row[2] for row in report_data['category_financial']]

            x = np.arange(len(categories))
            width = 0.35

            ax2.bar(x - width/2, budgets, width, label='الميزانية', color=self.colors[0])
            ax2.bar(x + width/2, costs, width, label='التكلفة الفعلية', color=self.colors[2])

            ax2.set_xlabel('التصنيفات')
            ax2.set_ylabel('المبلغ (ريال)')
            ax2.set_title('التوزيع المالي حسب التصنيف')
            ax2.set_xticks(x)
            ax2.set_xticklabels(categories, rotation=45, ha='right')
            ax2.legend()

        # الاتجاه المالي الشهري
        if report_data['monthly_financial']:
            months = [row[0] for row in reversed(report_data['monthly_financial'])]
            budgets = [row[1] for row in reversed(report_data['monthly_financial'])]
            costs = [row[2] for row in reversed(report_data['monthly_financial'])]

            ax3.plot(months, budgets, marker='o', label='الميزانية', color=self.colors[0], linewidth=2)
            ax3.plot(months, costs, marker='s', label='التكلفة الفعلية', color=self.colors[2], linewidth=2)

            ax3.set_title('الاتجاه المالي الشهري')
            ax3.set_ylabel('المبلغ (ريال)')
            ax3.tick_params(axis='x', rotation=45)
            ax3.legend()

        # رسم بياني دائري للتوزيع المالي حسب التصنيف
        if report_data['category_financial']:
            categories = [row[0] for row in report_data['category_financial']]
            budgets = [row[1] for row in report_data['category_financial']]

            ax4.pie(budgets, labels=categories, autopct='%1.1f%%', colors=self.colors)
            ax4.set_title('توزيع الميزانية حسب التصنيف')

        plt.tight_layout()
        return fig


class AdvancedReportsWidget(QWidget):
    """واجهة التقارير المتقدمة"""

    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.current_report_data = None
        self.visualization = ReportVisualization()
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # شريط التحكم العلوي
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # منطقة المحتوى الرئيسية
        main_splitter = QSplitter(Qt.Horizontal)

        # اللوحة اليسرى - الفلاتر والإعدادات
        filters_panel = self.create_filters_panel()
        main_splitter.addWidget(filters_panel)

        # اللوحة الوسطى - عرض التقارير
        self.reports_tab_widget = QTabWidget()
        main_splitter.addWidget(self.reports_tab_widget)

        # تعيين النسب
        main_splitter.setSizes([300, 900])
        layout.addWidget(main_splitter)

        self.setLayout(layout)

    def create_control_panel(self):
        """إنشاء لوحة التحكم العلوية"""
        panel = QGroupBox("إعدادات التقرير")
        layout = QHBoxLayout()

        # نوع التقرير
        layout.addWidget(QLabel("نوع التقرير:"))
        self.report_type_combo = QComboBox()
        report_types = [
            "تقرير ملخص",
            "تقرير مفصل",
            "تقرير الأداء",
            "تقرير جغرافي",
            "تقرير مالي"
        ]
        self.report_type_combo.addItems(report_types)
        layout.addWidget(self.report_type_combo)

        layout.addStretch()

        # أزرار التحكم
        self.generate_btn = QPushButton("إنتاج التقرير")
        self.generate_btn.clicked.connect(self.generate_report)
        layout.addWidget(self.generate_btn)

        self.export_btn = QPushButton("تصدير")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        layout.addWidget(self.export_btn)

        self.print_btn = QPushButton("طباعة")
        self.print_btn.clicked.connect(self.print_report)
        self.print_btn.setEnabled(False)
        layout.addWidget(self.print_btn)

        panel.setLayout(layout)
        return panel

    def create_filters_panel(self):
        """إنشاء لوحة الفلاتر"""
        panel = QGroupBox("فلاتر التقرير")
        layout = QFormLayout()

        # فلتر التاريخ
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-6))
        self.start_date.setCalendarPopup(True)
        layout.addRow("من تاريخ:", self.start_date)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        layout.addRow("إلى تاريخ:", self.end_date)

        # فلتر التصنيف
        self.category_filter = QComboBox()
        self.category_filter.setEditable(True)
        self.load_categories()
        layout.addRow("التصنيف:", self.category_filter)

        # فلتر المحافظة
        self.governorate_filter = QComboBox()
        self.governorate_filter.setEditable(True)
        self.load_governorates()
        layout.addRow("المحافظة:", self.governorate_filter)

        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "مخطط", "جاري", "مكتمل", "ملغي"])
        layout.addRow("الحالة:", self.status_filter)

        # فلتر الأولوية
        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["الكل", "عالي", "متوسط", "منخفض"])
        layout.addRow("الأولوية:", self.priority_filter)

        # خيارات إضافية
        layout.addRow("", QLabel(""))  # فاصل

        self.include_charts = QCheckBox("تضمين الرسوم البيانية")
        self.include_charts.setChecked(True)
        layout.addRow("", self.include_charts)

        self.include_details = QCheckBox("تضمين التفاصيل")
        self.include_details.setChecked(True)
        layout.addRow("", self.include_details)

        self.group_by_month = QCheckBox("تجميع حسب الشهر")
        layout.addRow("", self.group_by_month)

        panel.setLayout(layout)
        return panel

    def load_categories(self):
        """تحميل التصنيفات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT DISTINCT parent_category FROM categories ORDER BY parent_category")
            categories = ["الكل"] + [row[0] for row in cursor.fetchall()]
            self.category_filter.addItems(categories)
        except Exception as e:
            print(f"خطأ في تحميل التصنيفات: {e}")

    def load_governorates(self):
        """تحميل المحافظات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT DISTINCT governorate FROM locations ORDER BY governorate")
            governorates = ["الكل"] + [row[0] for row in cursor.fetchall() if row[0]]
            self.governorate_filter.addItems(governorates)
        except Exception as e:
            print(f"خطأ في تحميل المحافظات: {e}")

    def get_filters(self):
        """الحصول على الفلاتر المحددة"""
        filters = {}

        filters['start_date'] = self.start_date.date().toString("yyyy-MM-dd")
        filters['end_date'] = self.end_date.date().toString("yyyy-MM-dd")

        if self.category_filter.currentText() != "الكل":
            filters['category'] = self.category_filter.currentText()

        if self.governorate_filter.currentText() != "الكل":
            filters['governorate'] = self.governorate_filter.currentText()

        if self.status_filter.currentText() != "الكل":
            filters['status'] = self.status_filter.currentText()

        if self.priority_filter.currentText() != "الكل":
            filters['priority'] = self.priority_filter.currentText()

        filters['include_charts'] = self.include_charts.isChecked()
        filters['include_details'] = self.include_details.isChecked()
        filters['group_by_month'] = self.group_by_month.isChecked()

        return filters

    def generate_report(self):
        """إنتاج التقرير"""
        try:
            # تعطيل الأزرار أثناء الإنتاج
            self.generate_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # الحصول على نوع التقرير والفلاتر
            report_type = self.report_type_combo.currentData() or self.report_type_combo.currentText()
            if report_type in ["تقرير ملخص", "summary"]:
                report_type = "summary"
            elif report_type in ["تقرير مفصل", "detailed"]:
                report_type = "detailed"
            elif report_type in ["تقرير الأداء", "performance"]:
                report_type = "performance"
            elif report_type in ["تقرير جغرافي", "geographic"]:
                report_type = "geographic"
            elif report_type in ["تقرير مالي", "financial"]:
                report_type = "financial"

            filters = self.get_filters()

            # إنشاء مولد التقرير
            self.report_generator = ReportGenerator(report_type, filters, self.conn)
            self.report_generator.progress_updated.connect(self.progress_bar.setValue)
            self.report_generator.report_ready.connect(self.display_report)
            self.report_generator.error_occurred.connect(self.handle_error)
            self.report_generator.start()

        except Exception as e:
            self.handle_error(str(e))

    def display_report(self, report_data):
        """عرض التقرير"""
        try:
            self.current_report_data = report_data

            # مسح التبويبات السابقة
            self.reports_tab_widget.clear()

            # إضافة تبويب الملخص النصي
            summary_widget = self.create_summary_widget(report_data)
            self.reports_tab_widget.addTab(summary_widget, "الملخص")

            # إضافة تبويب الرسوم البيانية
            if self.include_charts.isChecked():
                charts_widget = self.create_charts_widget(report_data)
                self.reports_tab_widget.addTab(charts_widget, "الرسوم البيانية")

            # إضافة تبويب البيانات المفصلة
            if self.include_details.isChecked() and report_data['type'] == 'detailed':
                details_widget = self.create_details_widget(report_data)
                self.reports_tab_widget.addTab(details_widget, "البيانات المفصلة")

            # تفعيل أزرار التصدير والطباعة
            self.export_btn.setEnabled(True)
            self.print_btn.setEnabled(True)

        except Exception as e:
            self.handle_error(f"خطأ في عرض التقرير: {str(e)}")
        finally:
            # إعادة تفعيل الأزرار وإخفاء شريط التقدم
            self.generate_btn.setEnabled(True)
            self.progress_bar.setVisible(False)

    def create_summary_widget(self, report_data):
        """إنشاء ويدجت الملخص النصي"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان التقرير
        title = QLabel(f"تقرير {report_data['type']} - {report_data['generated_at']}")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # منطقة النص القابلة للتمرير
        scroll_area = QScrollArea()
        content_widget = QWidget()
        content_layout = QVBoxLayout()

        # إنشاء محتوى الملخص حسب نوع التقرير
        if report_data['type'] == 'summary':
            content_layout.addWidget(self.create_summary_content(report_data))
        elif report_data['type'] == 'performance':
            content_layout.addWidget(self.create_performance_content(report_data))
        elif report_data['type'] == 'financial':
            content_layout.addWidget(self.create_financial_content(report_data))
        elif report_data['type'] == 'geographic':
            content_layout.addWidget(self.create_geographic_content(report_data))

        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        widget.setLayout(layout)
        return widget

    def create_summary_content(self, report_data):
        """إنشاء محتوى التقرير الملخص"""
        widget = QWidget()
        layout = QVBoxLayout()

        # الإحصائيات العامة
        general_stats = report_data['general_stats']
        stats_text = f"""
        <h3>الإحصائيات العامة</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="width:100%">
        <tr><td><b>إجمالي الأنشطة</b></td><td>{general_stats['total_activities']}</td></tr>
        <tr><td><b>الأنشطة المكتملة</b></td><td>{general_stats['completed_activities']}</td></tr>
        <tr><td><b>معدل الإنجاز</b></td><td>{general_stats['completion_rate']:.1f}%</td></tr>
        <tr><td><b>إجمالي المشاركين</b></td><td>{general_stats['total_participants']:,}</td></tr>
        <tr><td><b>إجمالي الميزانية</b></td><td>{general_stats['total_budget']:,.0f} ريال</td></tr>
        <tr><td><b>إجمالي التكلفة</b></td><td>{general_stats['total_cost']:,.0f} ريال</td></tr>
        <tr><td><b>كفاءة الميزانية</b></td><td>{general_stats['budget_efficiency']:.1f}%</td></tr>
        </table>
        """

        stats_label = QLabel(stats_text)
        stats_label.setWordWrap(True)
        layout.addWidget(stats_label)

        # إحصائيات التصنيفات
        if report_data['category_stats']:
            category_text = "<h3>إحصائيات التصنيفات</h3>"
            category_text += "<table border='1' cellpadding='5' cellspacing='0' style='width:100%'>"
            category_text += "<tr><th>التصنيف</th><th>عدد الأنشطة</th><th>المشاركون</th><th>الميزانية</th></tr>"

            for row in report_data['category_stats']:
                category_text += f"<tr><td>{row[0]}</td><td>{row[1]}</td><td>{row[2] or 0}</td><td>{row[3] or 0:,.0f}</td></tr>"

            category_text += "</table>"
            category_label = QLabel(category_text)
            category_label.setWordWrap(True)
            layout.addWidget(category_label)

        widget.setLayout(layout)
        return widget

    def create_performance_content(self, report_data):
        """إنشاء محتوى تقرير الأداء"""
        widget = QWidget()
        layout = QVBoxLayout()

        # مؤشرات الأداء الرئيسية
        kpis = report_data['kpis']
        kpi_text = f"""
        <h3>مؤشرات الأداء الرئيسية</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="width:100%">
        <tr><td><b>معدل الإنجاز</b></td><td>{kpis['completion_rate']:.1f}%</td></tr>
        <tr><td><b>معدل تحقيق الأهداف</b></td><td>{kpis['target_achievement_rate']:.1f}%</td></tr>
        <tr><td><b>كفاءة الميزانية</b></td><td>{kpis['budget_efficiency']:.1f}%</td></tr>
        </table>
        """

        kpi_label = QLabel(kpi_text)
        kpi_label.setWordWrap(True)
        layout.addWidget(kpi_label)

        widget.setLayout(layout)
        return widget

    def create_financial_content(self, report_data):
        """إنشاء محتوى التقرير المالي"""
        widget = QWidget()
        layout = QVBoxLayout()

        # الملخص المالي
        financial_summary = report_data['financial_summary']
        financial_text = f"""
        <h3>الملخص المالي</h3>
        <table border="1" cellpadding="5" cellspacing="0" style="width:100%">
        <tr><td><b>إجمالي الميزانية</b></td><td>{financial_summary['total_budget']:,.0f} ريال</td></tr>
        <tr><td><b>إجمالي التكلفة</b></td><td>{financial_summary['total_cost']:,.0f} ريال</td></tr>
        <tr><td><b>الوفورات</b></td><td>{financial_summary['savings']:,.0f} ريال</td></tr>
        <tr><td><b>متوسط الميزانية للنشاط</b></td><td>{financial_summary['avg_budget_per_activity']:,.0f} ريال</td></tr>
        <tr><td><b>متوسط التكلفة للنشاط</b></td><td>{financial_summary['avg_cost_per_activity']:,.0f} ريال</td></tr>
        </table>
        """

        financial_label = QLabel(financial_text)
        financial_label.setWordWrap(True)
        layout.addWidget(financial_label)

        widget.setLayout(layout)
        return widget

    def create_geographic_content(self, report_data):
        """إنشاء محتوى التقرير الجغرافي"""
        widget = QWidget()
        layout = QVBoxLayout()

        # ملخص المحافظات
        if report_data['governorate_summary']:
            geo_text = "<h3>ملخص المحافظات</h3>"
            geo_text += "<table border='1' cellpadding='5' cellspacing='0' style='width:100%'>"
            geo_text += "<tr><th>المحافظة</th><th>عدد الأنشطة</th><th>المنفذون</th><th>المشاركون</th></tr>"

            for row in report_data['governorate_summary']:
                geo_text += f"<tr><td>{row[0]}</td><td>{row[1]}</td><td>{row[2]}</td><td>{row[3]}</td></tr>"

            geo_text += "</table>"
            geo_label = QLabel(geo_text)
            geo_label.setWordWrap(True)
            layout.addWidget(geo_label)

        widget.setLayout(layout)
        return widget

    def create_charts_widget(self, report_data):
        """إنشاء ويدجت الرسوم البيانية"""
        widget = QWidget()
        layout = QVBoxLayout()

        try:
            # إنشاء الرسم البياني حسب نوع التقرير
            if report_data['type'] == 'summary':
                fig = self.visualization.create_summary_charts(report_data)
            elif report_data['type'] == 'performance':
                fig = self.visualization.create_performance_charts(report_data)
            elif report_data['type'] == 'financial':
                fig = self.visualization.create_financial_charts(report_data)
            else:
                # رسم بياني افتراضي
                fig = plt.figure(figsize=(10, 6))
                plt.text(0.5, 0.5, 'لا توجد رسوم بيانية متاحة لهذا النوع من التقارير',
                        ha='center', va='center', fontsize=14)
                plt.axis('off')

            # إضافة الرسم البياني إلى الواجهة
            canvas = FigureCanvas(fig)
            layout.addWidget(canvas)

        except Exception as e:
            error_label = QLabel(f"خطأ في إنشاء الرسوم البيانية: {str(e)}")
            layout.addWidget(error_label)

        widget.setLayout(layout)
        return widget

    def create_details_widget(self, report_data):
        """إنشاء ويدجت البيانات المفصلة"""
        widget = QWidget()
        layout = QVBoxLayout()

        if report_data['type'] == 'detailed' and 'activities' in report_data:
            # إنشاء جدول البيانات
            table = QTableWidget()
            activities = report_data['activities']

            if activities:
                table.setRowCount(len(activities))
                table.setColumnCount(17)

                headers = [
                    "ID", "الشخص", "اسم النشاط", "التصنيف الأب", "التصنيف الابن",
                    "المحافظة", "المديرية", "المشاركون", "المستهدف",
                    "الميزانية", "التكلفة", "الحالة", "الأولوية", "تاريخ النشاط",
                    "تاريخ البداية", "تاريخ النهاية", "النتائج"
                ]
                table.setHorizontalHeaderLabels(headers)

                # تعبئة البيانات
                for row_idx, activity in enumerate(activities):
                    for col_idx, value in enumerate(activity):
                        item = QTableWidgetItem(str(value) if value is not None else "")
                        table.setItem(row_idx, col_idx, item)

                # تحسين مظهر الجدول
                table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
                table.setAlternatingRowColors(True)
                table.setSortingEnabled(True)

            layout.addWidget(table)
        else:
            layout.addWidget(QLabel("لا توجد بيانات مفصلة متاحة"))

        widget.setLayout(layout)
        return widget

    def handle_error(self, error_message):
        """معالجة الأخطاء"""
        QMessageBox.critical(self, "خطأ", f"حدث خطأ: {error_message}")
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)

    def export_report(self):
        """تصدير التقرير"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
            return

        # اختيار نوع التصدير
        export_types = [
            "Excel (*.xlsx)",
            "PDF (*.pdf)",
            "CSV (*.csv)",
            "JSON (*.json)"
        ]

        filename, selected_filter = QFileDialog.getSaveFileName(
            self, "تصدير التقرير",
            f"تقرير_{self.current_report_data['type']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            ";;".join(export_types)
        )

        if filename:
            try:
                if selected_filter.startswith("Excel"):
                    self.export_to_excel(filename)
                elif selected_filter.startswith("PDF"):
                    self.export_to_pdf(filename)
                elif selected_filter.startswith("CSV"):
                    self.export_to_csv(filename)
                elif selected_filter.startswith("JSON"):
                    self.export_to_json(filename)

                QMessageBox.information(self, "نجح", "تم تصدير التقرير بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في التصدير: {str(e)}")

    def export_to_excel(self, filename):
        """تصدير إلى Excel"""
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'

        # إنشاء ملف Excel بسيط
        if self.current_report_data['type'] == 'detailed' and 'activities' in self.current_report_data:
            df = pd.DataFrame(self.current_report_data['activities'])
            df.to_excel(filename, index=False, engine='openpyxl')
        else:
            # إنشاء ملف Excel للتقارير الأخرى
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                summary_df = pd.DataFrame([self.current_report_data])
                summary_df.to_excel(writer, sheet_name='ملخص', index=False)

    def export_to_pdf(self, filename):
        """تصدير إلى PDF"""
        if not filename.endswith('.pdf'):
            filename += '.pdf'
        # تنفيذ أساسي - يمكن تحسينه لاحقاً
        QMessageBox.information(self, "معلومات", f"سيتم حفظ التقرير في: {filename}")

    def export_to_csv(self, filename):
        """تصدير إلى CSV"""
        if not filename.endswith('.csv'):
            filename += '.csv'

        if self.current_report_data['type'] == 'detailed' and 'activities' in self.current_report_data:
            df = pd.DataFrame(self.current_report_data['activities'])
            df.to_csv(filename, index=False, encoding='utf-8-sig')
        else:
            # تصدير ملخص للتقارير الأخرى
            summary_data = []
            if 'general_stats' in self.current_report_data:
                for key, value in self.current_report_data['general_stats'].items():
                    summary_data.append({'المؤشر': key, 'القيمة': value})
            df = pd.DataFrame(summary_data)
            df.to_csv(filename, index=False, encoding='utf-8-sig')

    def export_to_json(self, filename):
        """تصدير إلى JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.current_report_data, f, ensure_ascii=False, indent=2)

    def print_report(self):
        """طباعة التقرير"""
        # سيتم تنفيذ هذه الدالة لاحقاً
        QMessageBox.information(self, "معلومات", "ميزة الطباعة قيد التطوير")
