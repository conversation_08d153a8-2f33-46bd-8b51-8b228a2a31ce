"""
وحدة تحسين الأداء والاستقرار
تتضمن تحسينات للذاكرة، قاعدة البيانات، والواجهة
"""

import sqlite3
import threading
import time
try:
    import psutil
except ImportError:
    psutil = None
import gc
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QProgressBar, QGroupBox, QFormLayout, QTextEdit,
    QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView,
    QCheckBox, QSpinBox, QSlider, QComboBox
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QColor
import logging

class PerformanceMonitor(QThread):
    """مراقب الأداء في الخلفية"""
    
    performance_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.running = True
        self.monitoring_interval = 5  # ثواني
        
    def run(self):
        """تشغيل مراقب الأداء"""
        while self.running:
            try:
                performance_data = self.collect_performance_data()
                self.performance_updated.emit(performance_data)
                time.sleep(self.monitoring_interval)
            except Exception as e:
                print(f"خطأ في مراقب الأداء: {e}")
                time.sleep(10)
                
    def collect_performance_data(self):
        """جمع بيانات الأداء"""
        try:
            if psutil is None:
                return {
                    'timestamp': datetime.now(),
                    'memory': {'total': 0, 'available': 0, 'percent': 0, 'used': 0},
                    'cpu': {'percent': 0, 'count': 1},
                    'disk': {'total': 0, 'used': 0, 'free': 0, 'percent': 0},
                    'process': {'memory_rss': 0, 'memory_vms': 0, 'cpu_percent': 0}
                }

            # معلومات الذاكرة
            memory_info = psutil.virtual_memory()

            # معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)

            # معلومات القرص
            disk_info = psutil.disk_usage('.')

            # معلومات العملية الحالية
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return {
                'timestamp': datetime.now(),
                'memory': {
                    'total': memory_info.total,
                    'available': memory_info.available,
                    'percent': memory_info.percent,
                    'used': memory_info.used
                },
                'cpu': {
                    'percent': cpu_percent,
                    'count': psutil.cpu_count()
                },
                'disk': {
                    'total': disk_info.total,
                    'used': disk_info.used,
                    'free': disk_info.free,
                    'percent': (disk_info.used / disk_info.total) * 100
                },
                'process': {
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'cpu_percent': process.cpu_percent()
                }
            }
        except Exception as e:
            print(f"خطأ في جمع بيانات الأداء: {e}")
            return {}
            
    def stop(self):
        """إيقاف المراقب"""
        self.running = False


class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    def __init__(self, conn):
        self.conn = conn
        
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        optimizations = []
        
        try:
            cursor = self.conn.cursor()
            
            # تحليل الجداول
            cursor.execute("ANALYZE")
            optimizations.append("تم تحليل الجداول")
            
            # إعادة بناء الفهارس
            cursor.execute("REINDEX")
            optimizations.append("تم إعادة بناء الفهارس")
            
            # تنظيف قاعدة البيانات
            cursor.execute("VACUUM")
            optimizations.append("تم تنظيف قاعدة البيانات")
            
            # تحسين إعدادات SQLite
            self.optimize_sqlite_settings()
            optimizations.append("تم تحسين إعدادات SQLite")
            
            # إنشاء فهارس إضافية
            self.create_performance_indexes()
            optimizations.append("تم إنشاء فهارس الأداء")
            
            self.conn.commit()
            
        except Exception as e:
            optimizations.append(f"خطأ: {str(e)}")
            
        return optimizations
        
    def optimize_sqlite_settings(self):
        """تحسين إعدادات SQLite"""
        cursor = self.conn.cursor()
        
        # تحسين إعدادات الأداء
        performance_settings = [
            "PRAGMA journal_mode = WAL",  # Write-Ahead Logging
            "PRAGMA synchronous = NORMAL",  # تحسين السرعة
            "PRAGMA cache_size = 10000",  # زيادة حجم الذاكرة المؤقتة
            "PRAGMA temp_store = MEMORY",  # استخدام الذاكرة للملفات المؤقتة
            "PRAGMA mmap_size = 268435456",  # 256MB memory mapping
        ]
        
        for setting in performance_settings:
            try:
                cursor.execute(setting)
            except Exception as e:
                print(f"خطأ في تطبيق الإعداد {setting}: {e}")
                
    def create_performance_indexes(self):
        """إنشاء فهارس لتحسين الأداء"""
        cursor = self.conn.cursor()
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_activities_date ON activities(activity_date)",
            "CREATE INDEX IF NOT EXISTS idx_activities_person ON activities(person)",
            "CREATE INDEX IF NOT EXISTS idx_activities_category ON activities(parent_category)",
            "CREATE INDEX IF NOT EXISTS idx_activities_status ON activities(status)",
            "CREATE INDEX IF NOT EXISTS idx_activities_governorate ON activities(governorate)",
            "CREATE INDEX IF NOT EXISTS idx_activities_created ON activities(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_activities_updated ON activities(updated_at)",
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                print(f"خطأ في إنشاء الفهرس: {e}")
                
    def get_database_statistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        cursor = self.conn.cursor()
        stats = {}
        
        try:
            # حجم قاعدة البيانات
            cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            stats['database_size'] = cursor.fetchone()[0]
            
            # عدد الجداول
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            stats['table_count'] = cursor.fetchone()[0]
            
            # عدد الفهارس
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
            stats['index_count'] = cursor.fetchone()[0]
            
            # إحصائيات الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            table_stats = {}
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                table_stats[table_name] = row_count
                
            stats['table_statistics'] = table_stats
            
        except Exception as e:
            stats['error'] = str(e)
            
        return stats


class MemoryManager:
    """مدير الذاكرة"""
    
    @staticmethod
    def cleanup_memory():
        """تنظيف الذاكرة"""
        # تشغيل جامع القمامة
        collected = gc.collect()

        # إحصائيات الذاكرة
        if psutil:
            memory_info = psutil.virtual_memory()
            return {
                'objects_collected': collected,
                'memory_percent': memory_info.percent,
                'memory_available': memory_info.available
            }
        else:
            return {
                'objects_collected': collected,
                'memory_percent': 0,
                'memory_available': 0
            }
        
    @staticmethod
    def get_memory_usage():
        """الحصول على استخدام الذاكرة"""
        if psutil:
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                'rss': memory_info.rss,  # Resident Set Size
                'vms': memory_info.vms,  # Virtual Memory Size
                'percent': process.memory_percent()
            }
        else:
            return {
                'rss': 0,
                'vms': 0,
                'percent': 0
            }


class ErrorHandler:
    """معالج الأخطاء المحسن"""
    
    def __init__(self):
        self.setup_logging()
        
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('application.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
    @staticmethod
    def handle_exception(exc_type, exc_value, exc_traceback):
        """معالجة الاستثناءات غير المتوقعة"""
        if issubclass(exc_type, KeyboardInterrupt):
            return
            
        logging.error("استثناء غير متوقع", exc_info=(exc_type, exc_value, exc_traceback))
        
    @staticmethod
    def log_error(message, exception=None):
        """تسجيل خطأ"""
        if exception:
            logging.error(f"{message}: {str(exception)}")
        else:
            logging.error(message)
            
    @staticmethod
    def log_warning(message):
        """تسجيل تحذير"""
        logging.warning(message)
        
    @staticmethod
    def log_info(message):
        """تسجيل معلومات"""
        logging.info(message)


class PerformanceWidget(QWidget):
    """واجهة مراقبة الأداء"""
    
    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.performance_monitor = None
        self.db_optimizer = DatabaseOptimizer(conn)
        self.error_handler = ErrorHandler()
        self.setup_ui()
        self.start_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("⚡ مراقب الأداء والتحسين")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #607D8B, stop:1 #455A64);
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تبويبات المراقبة
        tab_widget = QTabWidget()
        
        # تبويب مراقبة الأداء
        performance_tab = self.create_performance_tab()
        tab_widget.addTab(performance_tab, "مراقبة الأداء")
        
        # تبويب تحسين قاعدة البيانات
        database_tab = self.create_database_tab()
        tab_widget.addTab(database_tab, "تحسين قاعدة البيانات")
        
        # تبويب إدارة الذاكرة
        memory_tab = self.create_memory_tab()
        tab_widget.addTab(memory_tab, "إدارة الذاكرة")
        
        # تبويب سجل الأخطاء
        logs_tab = self.create_logs_tab()
        tab_widget.addTab(logs_tab, "سجل الأخطاء")
        
        layout.addWidget(tab_widget)
        self.setLayout(layout)
        
    def create_performance_tab(self):
        """إنشاء تبويب مراقبة الأداء"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # مؤشرات الأداء الحية
        performance_group = QGroupBox("مؤشرات الأداء الحية")
        performance_layout = QFormLayout()
        
        # مؤشرات الذاكرة
        self.memory_usage_label = QLabel("0%")
        performance_layout.addRow("استخدام الذاكرة:", self.memory_usage_label)
        
        self.memory_progress = QProgressBar()
        performance_layout.addRow("", self.memory_progress)
        
        # مؤشرات المعالج
        self.cpu_usage_label = QLabel("0%")
        performance_layout.addRow("استخدام المعالج:", self.cpu_usage_label)
        
        self.cpu_progress = QProgressBar()
        performance_layout.addRow("", self.cpu_progress)
        
        # مؤشرات القرص
        self.disk_usage_label = QLabel("0%")
        performance_layout.addRow("استخدام القرص:", self.disk_usage_label)
        
        self.disk_progress = QProgressBar()
        performance_layout.addRow("", self.disk_progress)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # إعدادات المراقبة
        settings_group = QGroupBox("إعدادات المراقبة")
        settings_layout = QFormLayout()
        
        self.monitoring_interval = QSpinBox()
        self.monitoring_interval.setMinimum(1)
        self.monitoring_interval.setMaximum(60)
        self.monitoring_interval.setValue(5)
        self.monitoring_interval.setSuffix(" ثانية")
        settings_layout.addRow("فترة المراقبة:", self.monitoring_interval)
        
        self.auto_cleanup = QCheckBox("تنظيف تلقائي للذاكرة")
        self.auto_cleanup.setChecked(True)
        settings_layout.addRow("", self.auto_cleanup)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # أزرار التحكم
        control_buttons = QHBoxLayout()
        
        start_monitoring_btn = QPushButton("▶️ بدء المراقبة")
        start_monitoring_btn.clicked.connect(self.start_monitoring)
        control_buttons.addWidget(start_monitoring_btn)
        
        stop_monitoring_btn = QPushButton("⏹️ إيقاف المراقبة")
        stop_monitoring_btn.clicked.connect(self.stop_monitoring)
        control_buttons.addWidget(stop_monitoring_btn)
        
        cleanup_btn = QPushButton("🧹 تنظيف الذاكرة")
        cleanup_btn.clicked.connect(self.cleanup_memory)
        control_buttons.addWidget(cleanup_btn)
        
        layout.addLayout(control_buttons)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget

    def create_database_tab(self):
        """إنشاء تبويب تحسين قاعدة البيانات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إحصائيات قاعدة البيانات
        stats_group = QGroupBox("إحصائيات قاعدة البيانات")
        stats_layout = QFormLayout()

        self.db_size_label = QLabel("جاري الحساب...")
        stats_layout.addRow("حجم قاعدة البيانات:", self.db_size_label)

        self.table_count_label = QLabel("جاري الحساب...")
        stats_layout.addRow("عدد الجداول:", self.table_count_label)

        self.index_count_label = QLabel("جاري الحساب...")
        stats_layout.addRow("عدد الفهارس:", self.index_count_label)

        self.records_count_label = QLabel("جاري الحساب...")
        stats_layout.addRow("عدد السجلات:", self.records_count_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        # أدوات التحسين
        optimization_group = QGroupBox("أدوات التحسين")
        optimization_layout = QVBoxLayout()

        # أزرار التحسين
        optimization_buttons = QHBoxLayout()

        analyze_btn = QPushButton("📊 تحليل الجداول")
        analyze_btn.clicked.connect(self.analyze_database)
        optimization_buttons.addWidget(analyze_btn)

        reindex_btn = QPushButton("🔄 إعادة بناء الفهارس")
        reindex_btn.clicked.connect(self.reindex_database)
        optimization_buttons.addWidget(reindex_btn)

        vacuum_btn = QPushButton("🧹 تنظيف قاعدة البيانات")
        vacuum_btn.clicked.connect(self.vacuum_database)
        optimization_buttons.addWidget(vacuum_btn)

        optimize_all_btn = QPushButton("⚡ تحسين شامل")
        optimize_all_btn.clicked.connect(self.optimize_all)
        optimization_buttons.addWidget(optimize_all_btn)

        optimization_layout.addLayout(optimization_buttons)

        # نتائج التحسين
        self.optimization_results = QTextEdit()
        self.optimization_results.setMaximumHeight(150)
        self.optimization_results.setReadOnly(True)
        optimization_layout.addWidget(QLabel("نتائج التحسين:"))
        optimization_layout.addWidget(self.optimization_results)

        optimization_group.setLayout(optimization_layout)
        layout.addWidget(optimization_group)

        # تحديث الإحصائيات
        self.update_database_stats()

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_memory_tab(self):
        """إنشاء تبويب إدارة الذاكرة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # معلومات الذاكرة
        memory_info_group = QGroupBox("معلومات الذاكرة")
        memory_info_layout = QFormLayout()

        self.process_memory_label = QLabel("جاري الحساب...")
        memory_info_layout.addRow("ذاكرة التطبيق:", self.process_memory_label)

        self.system_memory_label = QLabel("جاري الحساب...")
        memory_info_layout.addRow("ذاكرة النظام:", self.system_memory_label)

        self.memory_percent_label = QLabel("جاري الحساب...")
        memory_info_layout.addRow("نسبة الاستخدام:", self.memory_percent_label)

        memory_info_group.setLayout(memory_info_layout)
        layout.addWidget(memory_info_group)

        # أدوات إدارة الذاكرة
        memory_tools_group = QGroupBox("أدوات إدارة الذاكرة")
        memory_tools_layout = QVBoxLayout()

        # أزرار إدارة الذاكرة
        memory_buttons = QHBoxLayout()

        gc_btn = QPushButton("🗑️ تشغيل جامع القمامة")
        gc_btn.clicked.connect(self.run_garbage_collector)
        memory_buttons.addWidget(gc_btn)

        clear_cache_btn = QPushButton("🧹 مسح الذاكرة المؤقتة")
        clear_cache_btn.clicked.connect(self.clear_cache)
        memory_buttons.addWidget(clear_cache_btn)

        memory_tools_layout.addLayout(memory_buttons)

        # نتائج إدارة الذاكرة
        self.memory_results = QTextEdit()
        self.memory_results.setMaximumHeight(150)
        self.memory_results.setReadOnly(True)
        memory_tools_layout.addWidget(QLabel("نتائج إدارة الذاكرة:"))
        memory_tools_layout.addWidget(self.memory_results)

        memory_tools_group.setLayout(memory_tools_layout)
        layout.addWidget(memory_tools_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_logs_tab(self):
        """إنشاء تبويب سجل الأخطاء"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عرض السجلات
        logs_group = QGroupBox("سجل الأحداث والأخطاء")
        logs_layout = QVBoxLayout()

        # أزرار التحكم في السجلات
        logs_control = QHBoxLayout()

        refresh_logs_btn = QPushButton("🔄 تحديث")
        refresh_logs_btn.clicked.connect(self.refresh_logs)
        logs_control.addWidget(refresh_logs_btn)

        clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        clear_logs_btn.clicked.connect(self.clear_logs)
        logs_control.addWidget(clear_logs_btn)

        export_logs_btn = QPushButton("📤 تصدير السجلات")
        export_logs_btn.clicked.connect(self.export_logs)
        logs_control.addWidget(export_logs_btn)

        logs_control.addStretch()
        logs_layout.addLayout(logs_control)

        # منطقة عرض السجلات
        self.logs_display = QTextEdit()
        self.logs_display.setReadOnly(True)
        self.logs_display.setFont(QFont("Courier", 9))
        logs_layout.addWidget(self.logs_display)

        logs_group.setLayout(logs_layout)
        layout.addWidget(logs_group)

        # تحديث السجلات
        self.refresh_logs()

        widget.setLayout(layout)
        return widget

    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        if self.performance_monitor is None or not self.performance_monitor.isRunning():
            self.performance_monitor = PerformanceMonitor()
            self.performance_monitor.performance_updated.connect(self.update_performance_display)
            self.performance_monitor.monitoring_interval = self.monitoring_interval.value()
            self.performance_monitor.start()

    def stop_monitoring(self):
        """إيقاف مراقبة الأداء"""
        if self.performance_monitor and self.performance_monitor.isRunning():
            self.performance_monitor.stop()
            self.performance_monitor.wait()

    def update_performance_display(self, performance_data):
        """تحديث عرض الأداء"""
        try:
            # تحديث مؤشرات الذاكرة
            memory_percent = performance_data['memory']['percent']
            self.memory_usage_label.setText(f"{memory_percent:.1f}%")
            self.memory_progress.setValue(int(memory_percent))

            # تحديث مؤشرات المعالج
            cpu_percent = performance_data['cpu']['percent']
            self.cpu_usage_label.setText(f"{cpu_percent:.1f}%")
            self.cpu_progress.setValue(int(cpu_percent))

            # تحديث مؤشرات القرص
            disk_percent = performance_data['disk']['percent']
            self.disk_usage_label.setText(f"{disk_percent:.1f}%")
            self.disk_progress.setValue(int(disk_percent))

            # تنظيف تلقائي للذاكرة إذا كان مفعلاً
            if self.auto_cleanup.isChecked() and memory_percent > 80:
                self.cleanup_memory()

        except Exception as e:
            self.error_handler.log_error("خطأ في تحديث عرض الأداء", e)

    def cleanup_memory(self):
        """تنظيف الذاكرة"""
        try:
            result = MemoryManager.cleanup_memory()

            message = f"تم تنظيف {result['objects_collected']} كائن من الذاكرة\n"
            message += f"الذاكرة المتاحة: {result['memory_available'] / (1024**3):.2f} GB\n"
            message += f"نسبة استخدام الذاكرة: {result['memory_percent']:.1f}%"

            self.memory_results.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")

        except Exception as e:
            self.error_handler.log_error("خطأ في تنظيف الذاكرة", e)

    def update_database_stats(self):
        """تحديث إحصائيات قاعدة البيانات"""
        try:
            stats = self.db_optimizer.get_database_statistics()

            if 'error' not in stats:
                self.db_size_label.setText(f"{stats['database_size'] / (1024**2):.2f} MB")
                self.table_count_label.setText(str(stats['table_count']))
                self.index_count_label.setText(str(stats['index_count']))

                total_records = sum(stats['table_statistics'].values())
                self.records_count_label.setText(str(total_records))
            else:
                self.db_size_label.setText(f"خطأ: {stats['error']}")

        except Exception as e:
            self.error_handler.log_error("خطأ في تحديث إحصائيات قاعدة البيانات", e)

    def analyze_database(self):
        """تحليل قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("ANALYZE")
            self.conn.commit()

            message = f"[{datetime.now().strftime('%H:%M:%S')}] تم تحليل قاعدة البيانات بنجاح"
            self.optimization_results.append(message)

        except Exception as e:
            self.error_handler.log_error("خطأ في تحليل قاعدة البيانات", e)

    def reindex_database(self):
        """إعادة بناء فهارس قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("REINDEX")
            self.conn.commit()

            message = f"[{datetime.now().strftime('%H:%M:%S')}] تم إعادة بناء الفهارس بنجاح"
            self.optimization_results.append(message)

        except Exception as e:
            self.error_handler.log_error("خطأ في إعادة بناء الفهارس", e)

    def vacuum_database(self):
        """تنظيف قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("VACUUM")
            self.conn.commit()

            message = f"[{datetime.now().strftime('%H:%M:%S')}] تم تنظيف قاعدة البيانات بنجاح"
            self.optimization_results.append(message)
            self.update_database_stats()

        except Exception as e:
            self.error_handler.log_error("خطأ في تنظيف قاعدة البيانات", e)

    def optimize_all(self):
        """تحسين شامل لقاعدة البيانات"""
        try:
            optimizations = self.db_optimizer.optimize_database()

            for optimization in optimizations:
                message = f"[{datetime.now().strftime('%H:%M:%S')}] {optimization}"
                self.optimization_results.append(message)

            self.update_database_stats()

        except Exception as e:
            self.error_handler.log_error("خطأ في التحسين الشامل", e)

    def run_garbage_collector(self):
        """تشغيل جامع القمامة"""
        try:
            collected = gc.collect()
            message = f"[{datetime.now().strftime('%H:%M:%S')}] تم جمع {collected} كائن من القمامة"
            self.memory_results.append(message)

        except Exception as e:
            self.error_handler.log_error("خطأ في تشغيل جامع القمامة", e)

    def clear_cache(self):
        """مسح الذاكرة المؤقتة"""
        try:
            # مسح ذاكرة matplotlib المؤقتة
            import matplotlib.pyplot as plt
            plt.clf()
            plt.close('all')

            message = f"[{datetime.now().strftime('%H:%M:%S')}] تم مسح الذاكرة المؤقتة"
            self.memory_results.append(message)

        except Exception as e:
            self.error_handler.log_error("خطأ في مسح الذاكرة المؤقتة", e)

    def refresh_logs(self):
        """تحديث السجلات"""
        try:
            with open('application.log', 'r', encoding='utf-8') as f:
                logs = f.read()
                self.logs_display.setText(logs)

        except FileNotFoundError:
            self.logs_display.setText("لا يوجد ملف سجلات")
        except Exception as e:
            self.logs_display.setText(f"خطأ في قراءة السجلات: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        try:
            with open('application.log', 'w', encoding='utf-8') as f:
                f.write("")
            self.logs_display.clear()

        except Exception as e:
            self.error_handler.log_error("خطأ في مسح السجلات", e)

    def export_logs(self):
        """تصدير السجلات"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox

        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير السجلات",
                f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt)"
            )

            if filename:
                with open('application.log', 'r', encoding='utf-8') as source:
                    with open(filename, 'w', encoding='utf-8') as target:
                        target.write(source.read())

                QMessageBox.information(self, "نجح", "تم تصدير السجلات بنجاح")

        except Exception as e:
            self.error_handler.log_error("خطأ في تصدير السجلات", e)

    def closeEvent(self, event):
        """إيقاف المراقبة عند إغلاق النافذة"""
        self.stop_monitoring()
        event.accept()
