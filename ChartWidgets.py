"""
وحدات الرسوم البيانية المخصصة للنظام
تتضمن رسوم بيانية متحركة وتفاعلية مع دعم كامل للغة العربية
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QSizePolicy
from PyQt5.QtCore import Qt, QTimer, QRectF, QPointF
from PyQt5.QtGui import QPainter, QColor, QFont, QBrush, QPen, QLinearGradient, QRadialGradient, QPainterPath

# ألوان افتراضية للرسوم البيانية
DEFAULT_COLORS = [
    "#4e79a7", "#f28e2c", "#e15759", "#76b7b2", "#59a14f",
    "#edc949", "#af7aa1", "#ff9da7", "#9c755f", "#bab0ab"
]

class AnimatedPieChart(QWidget):
    """رسم بياني دائري متحرك"""

    def __init__(self, parent=None, rtl=True):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(200, 200)

        # إعدادات الرسم البياني
        self.title = ""
        self.data = []  # قائمة من الأزواج (الاسم، القيمة)
        self.colors = DEFAULT_COLORS
        self._animation_progress = 0  # استخدام متغير خاص لتجنب التكرار اللانهائي
        self.hover_segment = -1
        self.rtl = rtl  # دعم اللغة العربية (من اليمين إلى اليسار)

        # إعداد الحركة
        self.animation = QTimer()
        self.animation.setInterval(30)  # تحديث كل 30 مللي ثانية
        self.animation_duration = 1000  # مدة الحركة بالمللي ثانية
        self.animation_start_time = 0
        self.animation.timeout.connect(self.update_animation)

        # مؤقت لتحديث الرسم البياني
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_chart)
        self.update_timer.start(5000)  # تحديث كل 5 ثوانية

        # تخطيط العنوان
        self.layout = QVBoxLayout(self)
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        self.layout.addStretch()

    def set_title(self, title):
        """تعيين عنوان الرسم البياني"""
        self.title = title
        self.title_label.setText(title)

    def set_data(self, data):
        """تعيين بيانات الرسم البياني وبدء الحركة"""
        self.data = data
        self.animation.stop()
        self._animation_progress = 0

        # تعيين وقت بدء الحركة
        import time
        self.animation_start_time = time.time() * 1000

        # بدء المؤقت
        self.animation.start()

    def update_animation(self):
        """تحديث قيمة التحريك"""
        import time

        # حساب الوقت المنقضي منذ بدء الحركة
        elapsed = time.time() * 1000 - self.animation_start_time

        # حساب نسبة التقدم
        progress = min(1.0, elapsed / self.animation_duration)

        # تطبيق منحنى التسارع (easing curve)
        # هنا نستخدم منحنى تسارع بسيط (cubic ease out)
        t = 1.0 - progress
        eased_progress = 1.0 - t * t * t

        # تحديث قيمة التقدم
        self._animation_progress = eased_progress

        # إيقاف المؤقت عند انتهاء الحركة
        if progress >= 1.0:
            self.animation.stop()

        # تحديث الرسم
        self.update()

    def update_chart(self):
        """تحديث الرسم البياني (يمكن استخدامها لتحديث البيانات تلقائيًا)"""
        # يمكن تنفيذ منطق تحديث البيانات هنا
        self.update()

    def get_animation_progress(self):
        """الحصول على تقدم الحركة"""
        return self._animation_progress

    def set_animation_progress(self, progress):
        """تعيين تقدم الحركة"""
        self._animation_progress = progress
        self.update()

    # تعريف خاصية الحركة للرسم البياني
    animation_progress = property(get_animation_progress, set_animation_progress)

    def paintEvent(self, _):
        """رسم الرسم البياني"""
        if not self.data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # حساب المساحة المتاحة للرسم
        rect = self.rect()
        chart_rect = QRectF(rect.x() + 10, rect.y() + 40,
                           rect.width() - 20, rect.height() - 80)

        # حساب المركز ونصف القطر
        center = chart_rect.center()
        radius = min(chart_rect.width(), chart_rect.height()) / 2

        # حساب إجمالي القيم
        total = sum(value for _, value in self.data)
        if total <= 0:
            return

        # رسم القطاعات
        start_angle = 90 * 16  # زاوية البداية (بالدرجات × 16)

        for i, (name, value) in enumerate(self.data):
            # حساب الزاوية
            angle = int(360 * 16 * (value / total) * self._animation_progress)

            # اختيار اللون
            color = QColor(self.colors[i % len(self.colors)])

            # تأثير التحويم
            if i == self.hover_segment:
                # زيادة نصف القطر قليلاً للقطاع المحدد
                hover_radius = radius * 1.05
                painter.setBrush(QBrush(color.lighter(110)))
            else:
                hover_radius = radius
                painter.setBrush(QBrush(color))

            # رسم القطاع
            painter.setPen(QPen(Qt.white, 1))
            painter.drawPie(
                QRectF(center.x() - hover_radius, center.y() - hover_radius,
                      hover_radius * 2, hover_radius * 2),
                start_angle, -angle
            )

            # تحديث زاوية البداية للقطاع التالي
            start_angle -= angle

        # رسم المفتاح (Legend) بتنسيق أفضل
        # استخدام تخطيط عمودي للمفتاح بدلاً من أفقي لتجنب الازدحام
        legend_x = rect.x() + 20
        legend_y = rect.y() + 50  # بدء من أعلى الرسم البياني
        legend_spacing = 25  # زيادة المسافة بين عناصر المفتاح

        # فرز البيانات تنازلياً حسب القيمة لعرض العناصر الأكثر أهمية أولاً
        sorted_data = sorted(self.data, key=lambda x: x[1], reverse=True)

        # تحديد عدد الأعمدة للمفتاح
        legend_columns = 2
        items_per_column = (len(sorted_data) + legend_columns - 1) // legend_columns

        for i, (name, value) in enumerate(sorted_data):
            # حساب موضع العنصر في المفتاح (عمود وصف)
            column = i // items_per_column
            row = i % items_per_column

            # حساب الإحداثيات
            item_x = legend_x + column * (rect.width() / 2)
            item_y = legend_y + row * legend_spacing

            # رسم مربع اللون
            color = QColor(self.colors[i % len(self.colors)])
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(Qt.black, 1))
            painter.drawRect(int(item_x), int(item_y - 10), 15, 15)

            # كتابة النص
            painter.setPen(QPen(Qt.black))
            percentage = (value / total) * 100

            # تقصير النص إذا كان طويلاً جداً
            if len(name) > 15:
                display_name = name[:12] + "..."
            else:
                display_name = name

            text = f"{display_name}: {percentage:.1f}%"

            # ضبط اتجاه النص للغة العربية
            painter.drawText(int(item_x + 20), int(item_y), text)

class AnimatedBarChart(QWidget):
    """رسم بياني شريطي متحرك"""

    def __init__(self, parent=None, rtl=True):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(200, 200)

        # إعدادات الرسم البياني
        self.title = ""
        self.data = []  # قائمة من الأزواج (الاسم، القيمة)
        self.colors = DEFAULT_COLORS
        self._animation_progress = 0  # استخدام متغير خاص لتجنب التكرار اللانهائي
        self.hover_bar = -1
        self.rtl = rtl  # دعم اللغة العربية (من اليمين إلى اليسار)

        # إعداد الحركة
        self.animation = QTimer()
        self.animation.setInterval(30)  # تحديث كل 30 مللي ثانية
        self.animation_duration = 1000  # مدة الحركة بالمللي ثانية
        self.animation_start_time = 0
        self.animation.timeout.connect(self.update_animation)

        # تخطيط العنوان
        self.layout = QVBoxLayout(self)
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        self.layout.addStretch()

    def set_title(self, title):
        """تعيين عنوان الرسم البياني"""
        self.title = title
        self.title_label.setText(title)

    def set_data(self, data):
        """تعيين بيانات الرسم البياني وبدء الحركة"""
        self.data = data
        self.animation.stop()
        self._animation_progress = 0

        # تعيين وقت بدء الحركة
        import time
        self.animation_start_time = time.time() * 1000

        # بدء المؤقت
        self.animation.start()

    def update_animation(self):
        """تحديث قيمة التحريك"""
        import time

        # حساب الوقت المنقضي منذ بدء الحركة
        elapsed = time.time() * 1000 - self.animation_start_time

        # حساب نسبة التقدم
        progress = min(1.0, elapsed / self.animation_duration)

        # تطبيق منحنى التسارع (easing curve)
        # هنا نستخدم منحنى تسارع بسيط (bounce ease out)
        t = progress
        if t < (1/2.75):
            eased_progress = 7.5625 * t * t
        elif t < (2/2.75):
            t = t - (1.5/2.75)
            eased_progress = 7.5625 * t * t + 0.75
        elif t < (2.5/2.75):
            t = t - (2.25/2.75)
            eased_progress = 7.5625 * t * t + 0.9375
        else:
            t = t - (2.625/2.75)
            eased_progress = 7.5625 * t * t + 0.984375

        # تحديث قيمة التقدم
        self._animation_progress = eased_progress

        # إيقاف المؤقت عند انتهاء الحركة
        if progress >= 1.0:
            self.animation.stop()

        # تحديث الرسم
        self.update()

    def get_animation_progress(self):
        """الحصول على تقدم الحركة"""
        return self._animation_progress

    def set_animation_progress(self, progress):
        """تعيين تقدم الحركة"""
        self._animation_progress = progress
        self.update()

    # تعريف خاصية الحركة للرسم البياني
    animation_progress = property(get_animation_progress, set_animation_progress)

    def paintEvent(self, _):
        """رسم الرسم البياني"""
        if not self.data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # حساب المساحة المتاحة للرسم
        rect = self.rect()
        chart_rect = QRectF(rect.x() + 50, rect.y() + 40,
                           rect.width() - 70, rect.height() - 80)

        # حساب القيمة القصوى
        max_value = max(value for _, value in self.data) if self.data else 0
        if max_value <= 0:
            return

        # حساب عرض الأشرطة والمسافة بينها مع زيادة المسافة
        bar_count = len(self.data)
        bar_width = chart_rect.width() / (bar_count * 2.0)  # تقليل عرض الأشرطة
        spacing = bar_width  # زيادة المسافة بين الأشرطة

        # رسم المحاور
        painter.setPen(QPen(Qt.darkGray, 1))
        # المحور الأفقي
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.bottom()),
            int(chart_rect.right()), int(chart_rect.bottom())
        )
        # المحور الرأسي
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.top()),
            int(chart_rect.left()), int(chart_rect.bottom())
        )

        # رسم الأشرطة
        for i, (name, value) in enumerate(self.data):
            # حساب موضع الشريط
            x = chart_rect.left() + i * (bar_width + spacing)

            # حساب ارتفاع الشريط مع الحركة
            height = (value / max_value) * chart_rect.height() * self._animation_progress
            y = chart_rect.bottom() - height

            # إنشاء تدرج لوني للشريط
            gradient = QLinearGradient(
                x, y, x + bar_width, chart_rect.bottom()
            )
            color = QColor(self.colors[i % len(self.colors)])
            gradient.setColorAt(0, color.lighter(130))
            gradient.setColorAt(1, color)

            # رسم الشريط
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(Qt.darkGray, 1))

            # تأثير التحويم
            if i == self.hover_bar:
                # زيادة عرض الشريط المحدد قليلاً
                hover_width = bar_width * 1.1
                hover_x = x - (hover_width - bar_width) / 2
                painter.drawRoundedRect(
                    int(hover_x), int(y), int(hover_width), int(height), 5, 5
                )
            else:
                painter.drawRoundedRect(
                    int(x), int(y), int(bar_width), int(height), 5, 5
                )

            # كتابة القيمة فوق الشريط
            painter.setPen(QPen(Qt.black))
            value_text = f"{value:.1f}"
            text_width = painter.fontMetrics().horizontalAdvance(value_text)
            painter.drawText(
                int(x + (bar_width - text_width) / 2),
                int(y - 5),
                value_text
            )

            # كتابة اسم الشريط أسفله بشكل مائل لتجنب التداخل
            # تقصير النص إذا كان طويلاً جداً
            if len(name) > 12:
                display_name = name[:10] + "..."
            else:
                display_name = name

            name_width = painter.fontMetrics().horizontalAdvance(display_name)
            text_x = x + (bar_width - name_width) / 2

            # ضبط اتجاه النص للغة العربية مع إمالة النص لتجنب التداخل
            painter.save()

            if self.rtl:
                # للنص العربي، نستخدم زاوية مختلفة
                painter.translate(text_x + name_width/2, chart_rect.bottom() + 15)
                painter.rotate(-45)  # إمالة النص بزاوية 45 درجة
                painter.drawText(0, 0, display_name)
            else:
                painter.translate(text_x + name_width/2, chart_rect.bottom() + 15)
                painter.rotate(-45)  # إمالة النص بزاوية 45 درجة
                painter.drawText(-name_width/2, 0, display_name)

            painter.restore()

class AnimatedLineChart(QWidget):
    """رسم بياني خطي متحرك"""

    def __init__(self, parent=None, rtl=True):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(200, 200)

        # إعدادات الرسم البياني
        self.title = ""
        self.data = []  # قائمة من الأزواج (الاسم، [قائمة القيم])
        self.labels = []  # تسميات المحور الأفقي
        self.colors = DEFAULT_COLORS
        self._animation_progress = 0  # استخدام متغير خاص لتجنب التكرار اللانهائي
        self.rtl = rtl  # دعم اللغة العربية (من اليمين إلى اليسار)

        # إعداد الحركة
        self.animation = QTimer()
        self.animation.setInterval(30)  # تحديث كل 30 مللي ثانية
        self.animation_duration = 1500  # مدة الحركة بالمللي ثانية
        self.animation_start_time = 0
        self.animation.timeout.connect(self.update_animation)

        # تخطيط العنوان
        self.layout = QVBoxLayout(self)
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.layout.addWidget(self.title_label)
        self.layout.addStretch()

    def set_title(self, title):
        """تعيين عنوان الرسم البياني"""
        self.title = title
        self.title_label.setText(title)

    def set_data(self, data, labels=None):
        """تعيين بيانات الرسم البياني وبدء الحركة"""
        self.data = data
        if labels:
            self.labels = labels
        self.animation.stop()
        self._animation_progress = 0

        # تعيين وقت بدء الحركة
        import time
        self.animation_start_time = time.time() * 1000

        # بدء المؤقت
        self.animation.start()

    def update_animation(self):
        """تحديث قيمة التحريك"""
        import time

        # حساب الوقت المنقضي منذ بدء الحركة
        elapsed = time.time() * 1000 - self.animation_start_time

        # حساب نسبة التقدم
        progress = min(1.0, elapsed / self.animation_duration)

        # تطبيق منحنى التسارع (easing curve)
        # هنا نستخدم منحنى تسارع بسيط (quad ease out)
        eased_progress = -progress * (progress - 2)

        # تحديث قيمة التقدم
        self._animation_progress = eased_progress

        # إيقاف المؤقت عند انتهاء الحركة
        if progress >= 1.0:
            self.animation.stop()

        # تحديث الرسم
        self.update()

    def get_animation_progress(self):
        """الحصول على تقدم الحركة"""
        return self._animation_progress

    def set_animation_progress(self, progress):
        """تعيين تقدم الحركة"""
        self._animation_progress = progress
        self.update()

    # تعريف خاصية الحركة للرسم البياني
    animation_progress = property(get_animation_progress, set_animation_progress)

    def paintEvent(self, _):
        """رسم الرسم البياني"""
        if not self.data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # حساب المساحة المتاحة للرسم
        rect = self.rect()
        chart_rect = QRectF(rect.x() + 50, rect.y() + 40,
                           rect.width() - 70, rect.height() - 80)

        # حساب القيمة القصوى
        max_value = 0
        for _, values in self.data:
            if values:
                max_value = max(max_value, max(values))

        if max_value <= 0:
            return

        # رسم المحاور
        painter.setPen(QPen(Qt.darkGray, 1))
        # المحور الأفقي
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.bottom()),
            int(chart_rect.right()), int(chart_rect.bottom())
        )
        # المحور الرأسي
        painter.drawLine(
            int(chart_rect.left()), int(chart_rect.top()),
            int(chart_rect.left()), int(chart_rect.bottom())
        )

        # رسم خطوط الشبكة الأفقية (للقيم)
        grid_count = 5  # عدد خطوط الشبكة الأفقية
        painter.setPen(QPen(QColor(200, 200, 200, 100), 1, Qt.DashLine))

        for i in range(1, grid_count + 1):
            y = chart_rect.bottom() - (i * chart_rect.height() / grid_count)
            painter.drawLine(
                int(chart_rect.left()), int(y),
                int(chart_rect.right()), int(y)
            )

            # إضافة قيم على المحور الرأسي
            value = (i * max_value / grid_count)
            value_text = f"{value:.0f}"
            text_width = painter.fontMetrics().horizontalAdvance(value_text)
            painter.setPen(QPen(Qt.black))
            painter.drawText(
                int(chart_rect.left() - text_width - 5),
                int(y + 5),
                value_text
            )

        # إعادة تعيين القلم للرسم البياني
        painter.setPen(QPen(Qt.darkGray, 1))

        # رسم تسميات المحور الأفقي
        if self.labels:
            label_count = len(self.labels)
            step = chart_rect.width() / (label_count - 1) if label_count > 1 else chart_rect.width()

            for i, label in enumerate(self.labels):
                x = chart_rect.left() + i * step

                # رسم خط علامة
                painter.setPen(QPen(Qt.lightGray, 1, Qt.DotLine))
                painter.drawLine(
                    int(x), int(chart_rect.top()),
                    int(x), int(chart_rect.bottom())
                )

                # كتابة التسمية
                painter.setPen(QPen(Qt.black))
                text_width = painter.fontMetrics().horizontalAdvance(label)

                # ضبط اتجاه النص للغة العربية مع إمالة لتجنب التداخل
                painter.save()

                # تقصير التسمية إذا كانت طويلة
                if len(label) > 12:
                    display_label = label[:10] + "..."
                else:
                    display_label = label

                text_width = painter.fontMetrics().horizontalAdvance(display_label)

                if self.rtl:
                    painter.translate(x, chart_rect.bottom() + 15)
                    painter.rotate(-45)  # إمالة النص بزاوية 45 درجة
                    painter.drawText(0, 0, display_label)
                else:
                    painter.translate(x, chart_rect.bottom() + 15)
                    painter.rotate(-45)  # إمالة النص بزاوية 45 درجة
                    painter.drawText(-text_width/2, 0, display_label)

                painter.restore()

        # رسم الخطوط والنقاط
        for series_idx, (name, values) in enumerate(self.data):
            if not values:
                continue

            # اختيار اللون
            color = QColor(self.colors[series_idx % len(self.colors)])

            # إنشاء مسار للخط
            path = QPainterPath()
            points = []

            # حساب عدد النقاط المرئية بناءً على تقدم الحركة
            visible_points = int(len(values) * self._animation_progress)

            # حساب المسافة بين النقاط
            point_count = len(values)
            step = chart_rect.width() / (point_count - 1) if point_count > 1 else chart_rect.width()

            # إنشاء نقاط المسار
            for i in range(visible_points):
                x = chart_rect.left() + i * step
                y = chart_rect.bottom() - (values[i] / max_value) * chart_rect.height()

                if i == 0:
                    path.moveTo(x, y)
                else:
                    path.lineTo(x, y)

                points.append((x, y))

            # رسم الخط
            painter.setPen(QPen(color, 2))
            painter.drawPath(path)

            # رسم النقاط
            for x, y in points:
                # تأثير توهج للنقاط
                radial = QRadialGradient(x, y, 5)
                radial.setColorAt(0, color.lighter(150))
                radial.setColorAt(1, color)

                painter.setBrush(QBrush(radial))
                painter.setPen(QPen(Qt.white, 1))
                painter.drawEllipse(QPointF(x, y), 4, 4)

        # رسم المفتاح (Legend) بتنسيق أفضل
        legend_x = rect.x() + 20
        legend_y = rect.y() + 20
        legend_spacing = 25  # زيادة المسافة بين عناصر المفتاح

        # تحديد عدد الأعمدة للمفتاح
        legend_columns = 2
        items_per_column = (len(self.data) + legend_columns - 1) // legend_columns

        for i, (name, _) in enumerate(self.data):
            # حساب موضع العنصر في المفتاح (عمود وصف)
            column = i // items_per_column
            row = i % items_per_column

            # حساب الإحداثيات
            item_x = legend_x + column * (rect.width() / 2)
            item_y = legend_y + row * legend_spacing

            # رسم خط اللون
            color = QColor(self.colors[i % len(self.colors)])
            painter.setPen(QPen(color, 2))
            painter.drawLine(
                int(item_x), int(item_y),
                int(item_x + 20), int(item_y)
            )

            # رسم نقطة
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(Qt.white, 1))
            painter.drawEllipse(QPointF(item_x + 10, item_y), 3, 3)

            # تقصير النص إذا كان طويلاً جداً
            if len(name) > 15:
                display_name = name[:12] + "..."
            else:
                display_name = name

            # كتابة الاسم
            painter.setPen(QPen(Qt.black))
            painter.drawText(int(item_x + 25), int(item_y + 5), display_name)
