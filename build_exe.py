#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بناء التطبيق التنفيذي (.exe)
يقوم بتحويل نظام إدارة الأنشطة المتقدم إلى ملف تنفيذي
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json

class ActivitySystemBuilder:
    """فئة بناء نظام إدارة الأنشطة"""
    
    def __init__(self):
        self.app_name = "نظام إدارة الأنشطة المتقدم"
        self.app_version = "2.0"
        self.main_script = "run_app.py"
        self.build_dir = "build"
        self.dist_dir = "dist"
        self.icon_file = "app_icon.ico"
        
    def check_requirements(self):
        """فحص المتطلبات المطلوبة للبناء"""
        print("🔍 فحص المتطلبات...")
        
        required_packages = [
            'PyInstaller',
            'PyQt5',
            'matplotlib',
            'pandas',
            'openpyxl',
            'reportlab',
            'psutil',
            'schedule'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.lower().replace('-', '_'))
                print(f"  ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package}")
        
        if missing_packages:
            print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
            print("تثبيت المكتبات المفقودة...")
            
            for package in missing_packages:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    print(f"  ✅ تم تثبيت {package}")
                except subprocess.CalledProcessError:
                    print(f"  ❌ فشل تثبيت {package}")
                    return False
        
        return True
    
    def create_icon(self):
        """إنشاء أيقونة التطبيق"""
        print("🎨 إنشاء أيقونة التطبيق...")
        
        if os.path.exists(self.icon_file):
            print(f"  ✅ الأيقونة موجودة: {self.icon_file}")
            return True
        
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # إنشاء أيقونة بسيطة
            size = (256, 256)
            img = Image.new('RGBA', size, (76, 175, 80, 255))  # أخضر
            draw = ImageDraw.Draw(img)
            
            # رسم دائرة
            margin = 20
            draw.ellipse([margin, margin, size[0]-margin, size[1]-margin], 
                        fill=(255, 255, 255, 255), outline=(33, 150, 243, 255), width=8)
            
            # إضافة نص
            try:
                font = ImageFont.truetype("arial.ttf", 60)
            except:
                font = ImageFont.load_default()
            
            text = "🏛️"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (size[0] - text_width) // 2
            y = (size[1] - text_height) // 2
            
            draw.text((x, y), text, fill=(33, 150, 243, 255), font=font)
            
            # حفظ كـ ICO
            img.save(self.icon_file, format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
            print(f"  ✅ تم إنشاء الأيقونة: {self.icon_file}")
            return True
            
        except ImportError:
            print("  ⚠️  Pillow غير متوفر، سيتم استخدام أيقونة افتراضية")
            return False
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء الأيقونة: {e}")
            return False
    
    def create_spec_file(self):
        """إنشاء ملف المواصفات لـ PyInstaller"""
        print("📝 إنشاء ملف المواصفات...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# قائمة الملفات الإضافية
added_files = [
    ('requirements.txt', '.'),
    ('README.md', '.'),
    ('Templates_Guide.md', '.'),
]

# إضافة الأيقونة إذا كانت موجودة
icon_path = '{self.icon_file}' if os.path.exists('{self.icon_file}') else None

a = Analysis(
    ['{self.main_script}'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'matplotlib.backends.backend_qt5agg',
        'pandas',
        'openpyxl',
        'reportlab',
        'psutil',
        'schedule',
        'sqlite3',
        'csv',
        'json',
        'datetime',
        'threading',
        'logging'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'test',
        'unittest',
        'pydoc',
        'doctest'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name.replace(" ", "_")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة الكونسول
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,
    version_file=None,
)
'''
        
        spec_filename = f"{self.app_name.replace(' ', '_')}.spec"
        
        with open(spec_filename, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print(f"  ✅ تم إنشاء ملف المواصفات: {spec_filename}")
        return spec_filename
    
    def create_version_file(self):
        """إنشاء ملف معلومات الإصدار"""
        print("📋 إنشاء ملف معلومات الإصدار...")
        
        version_info = f'''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(2, 0, 0, 0),
    prodvers=(2, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'فريق التطوير المتخصص'),
        StringStruct(u'FileDescription', u'{self.app_name}'),
        StringStruct(u'FileVersion', u'{self.app_version}'),
        StringStruct(u'InternalName', u'ActivitySystem'),
        StringStruct(u'LegalCopyright', u'© 2024 جميع الحقوق محفوظة'),
        StringStruct(u'OriginalFilename', u'{self.app_name.replace(" ", "_")}.exe'),
        StringStruct(u'ProductName', u'{self.app_name}'),
        StringStruct(u'ProductVersion', u'{self.app_version}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        version_filename = "version_info.txt"
        
        with open(version_filename, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        print(f"  ✅ تم إنشاء ملف معلومات الإصدار: {version_filename}")
        return version_filename
    
    def clean_build_dirs(self):
        """تنظيف مجلدات البناء السابقة"""
        print("🧹 تنظيف مجلدات البناء السابقة...")
        
        dirs_to_clean = [self.build_dir, self.dist_dir, "__pycache__"]
        
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"  ✅ تم حذف: {dir_name}")
        
        # حذف ملفات .pyc
        for root, dirs, files in os.walk("."):
            for file in files:
                if file.endswith('.pyc'):
                    os.remove(os.path.join(root, file))
    
    def build_executable(self):
        """بناء الملف التنفيذي"""
        print("🔨 بناء الملف التنفيذي...")
        
        # إنشاء ملف المواصفات
        spec_file = self.create_spec_file()
        
        try:
            # تشغيل PyInstaller
            cmd = [
                'pyinstaller',
                '--clean',
                '--noconfirm',
                spec_file
            ]
            
            print(f"  🚀 تشغيل الأمر: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("  ✅ تم بناء الملف التنفيذي بنجاح!")
                return True
            else:
                print(f"  ❌ خطأ في البناء:")
                print(result.stderr)
                return False
                
        except FileNotFoundError:
            print("  ❌ PyInstaller غير موجود. يرجى تثبيته أولاً:")
            print("     pip install pyinstaller")
            return False
        except Exception as e:
            print(f"  ❌ خطأ غير متوقع: {e}")
            return False
    
    def create_installer_script(self):
        """إنشاء سكريبت تثبيت"""
        print("📦 إنشاء سكريبت التثبيت...")
        
        installer_script = f'''@echo off
chcp 65001 > nul
echo.
echo ===============================================
echo    {self.app_name}
echo    الإصدار {self.app_version}
echo ===============================================
echo.

echo 📋 بدء عملية التثبيت...
echo.

REM إنشاء مجلد التثبيت
set INSTALL_DIR=%PROGRAMFILES%\\ActivitySystem
echo 📁 إنشاء مجلد التثبيت: %INSTALL_DIR%
mkdir "%INSTALL_DIR%" 2>nul

REM نسخ الملفات
echo 📄 نسخ ملفات التطبيق...
copy "{self.app_name.replace(" ", "_")}.exe" "%INSTALL_DIR%\\" >nul
copy "README.md" "%INSTALL_DIR%\\" >nul 2>nul
copy "Templates_Guide.md" "%INSTALL_DIR%\\" >nul 2>nul

REM إنشاء اختصار على سطح المكتب
echo 🔗 إنشاء اختصار على سطح المكتب...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\{self.app_name.replace(" ", "_")}.exe'; $Shortcut.Save()"

REM إنشاء اختصار في قائمة ابدأ
echo 📌 إنشاء اختصار في قائمة ابدأ...
mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\ActivitySystem" 2>nul
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\ActivitySystem\\{self.app_name}.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\{self.app_name.replace(" ", "_")}.exe'; $Shortcut.Save()"

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 🚀 يمكنك الآن تشغيل التطبيق من:
echo    - سطح المكتب
echo    - قائمة ابدأ
echo    - المجلد: %INSTALL_DIR%
echo.
pause
'''
        
        installer_filename = "install.bat"
        
        with open(os.path.join(self.dist_dir, installer_filename), 'w', encoding='utf-8') as f:
            f.write(installer_script)
        
        print(f"  ✅ تم إنشاء سكريبت التثبيت: {installer_filename}")
    
    def create_readme_for_dist(self):
        """إنشاء ملف README للتوزيع"""
        print("📖 إنشاء ملف README للتوزيع...")
        
        readme_content = f'''# {self.app_name} - الإصدار {self.app_version}

## 🚀 التشغيل السريع

### الطريقة الأولى: التشغيل المباشر
1. انقر نقراً مزدوجاً على ملف `{self.app_name.replace(" ", "_")}.exe`
2. انتظر حتى يتم تحميل التطبيق
3. ابدأ في استخدام النظام

### الطريقة الثانية: التثبيت الكامل
1. انقر نقراً مزدوجاً على ملف `install.bat`
2. اتبع تعليمات التثبيت
3. استخدم الاختصارات المُنشأة على سطح المكتب أو قائمة ابدأ

## 📋 متطلبات النظام

- نظام التشغيل: Windows 10/11 (64-bit)
- الذاكرة: 4 GB RAM (8 GB مستحسن)
- مساحة القرص: 200 MB للتطبيق + مساحة إضافية للبيانات
- دقة الشاشة: 1024x768 أو أعلى

## 🔧 استكشاف الأخطاء

### إذا لم يعمل التطبيق:
1. تأكد من أن نظام التشغيل محدث
2. قم بتشغيل التطبيق كمدير (Run as Administrator)
3. تأكد من وجود مساحة كافية على القرص
4. أعد تشغيل الكمبيوتر وحاول مرة أخرى

### إذا ظهرت رسائل خطأ:
1. تحقق من ملف السجلات (application.log) إن وُجد
2. تأكد من عدم تشغيل برامج مكافحة الفيروسات التي قد تحجب التطبيق
3. جرب تشغيل التطبيق في وضع التوافق مع Windows 8

## 📞 الدعم الفني

للحصول على المساعدة:
- راجع دليل المستخدم المدمج في التطبيق
- تواصل مع فريق الدعم الفني

---
© 2024 جميع الحقوق محفوظة
'''
        
        readme_filename = os.path.join(self.dist_dir, "اقرأني_أولاً.txt")
        
        with open(readme_filename, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"  ✅ تم إنشاء ملف README للتوزيع")
    
    def build(self):
        """تشغيل عملية البناء الكاملة"""
        print("🏗️  بدء بناء نظام إدارة الأنشطة المتقدم")
        print("=" * 50)
        
        # فحص المتطلبات
        if not self.check_requirements():
            print("❌ فشل في فحص المتطلبات")
            return False
        
        # تنظيف المجلدات السابقة
        self.clean_build_dirs()
        
        # إنشاء الأيقونة
        self.create_icon()
        
        # بناء الملف التنفيذي
        if not self.build_executable():
            print("❌ فشل في بناء الملف التنفيذي")
            return False
        
        # إنشاء ملفات إضافية للتوزيع
        self.create_installer_script()
        self.create_readme_for_dist()
        
        print("\n" + "=" * 50)
        print("🎉 تم بناء التطبيق بنجاح!")
        print(f"📁 الملفات متوفرة في مجلد: {self.dist_dir}")
        print(f"🚀 الملف التنفيذي: {self.app_name.replace(' ', '_')}.exe")
        print("📦 ملف التثبيت: install.bat")
        print("📖 دليل الاستخدام: اقرأني_أولاً.txt")
        
        return True

def main():
    """الدالة الرئيسية"""
    builder = ActivitySystemBuilder()
    
    try:
        success = builder.build()
        if success:
            input("\nاضغط Enter للخروج...")
        else:
            input("\nحدث خطأ. اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إلغاء العملية بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
