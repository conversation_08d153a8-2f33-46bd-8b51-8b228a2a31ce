@echo off
chcp 65001 > nul
title بناء نظام إدارة الأنشطة المتقدم

echo.
echo ===============================================
echo    🏗️  بناء نظام إدارة الأنشطة المتقدم
echo    الإصدار 2.0
echo ===============================================
echo.

echo 📋 التحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8+ أولاً
    pause
    exit /b 1
)

echo.
echo 📦 التحقق من PyInstaller...
pip show pyinstaller >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  PyInstaller غير مثبت، جاري التثبيت...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
)

echo.
echo 🚀 بدء عملية البناء...
python build_exe.py

echo.
echo ✅ انتهت عملية البناء
pause
