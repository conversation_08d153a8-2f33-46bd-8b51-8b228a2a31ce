import sys
from PyQt5.QtWidgets import (
    QDialog
)
class ModernDialog(QDialog):
    def __init__(self, parent=None, title="", fullscreen=False):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.fullscreen = fullscreen
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: Arial;
            }
            QLabel {
                font-weight: bold;
                color: #333;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QLineEdit, QComboBox, QSpinBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QTableWidget {
                border: 1px solid #ddd;
                selection-background-color: #e0f7fa;
            }
        """)

    def showEvent(self, event):
        """تنفيذ عند ظهور النافذة"""
        super().showEvent(event)
        if self.fullscreen:
            self.showMaximized()