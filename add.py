import sqlite3
import random
from datetime import datetime, timedelta

conn = sqlite3.connect("activities.db")
cursor = conn.cursor()

# بيانات عشوائية للاختبار
people = ['أحمد', 'فاطمة', 'محمد', 'سعاد', 'خالد', 'ليلى']
governorates = ['صنعاء', 'عدن', 'تعز', 'حضرموت']
districts = ['التحرير', 'المعلا', 'القاهرة', 'سيئون']
sub_districts = ['الوسطى', 'الشمالية', 'الجنوبية']
villages = ['القرية 1', 'القرية 2', 'القرية 3']
results = ['نجاح كبير', 'نجاح متوسط', 'تحديات واجهت التنفيذ']
recommendations = ['زيادة الدعم', 'تكرار النشاط', 'تطوير الأدوات']
notes = ['لا توجد ملاحظات', 'تم التنفيذ بنجاح', 'توصيات إضافية']
is_approved_choices = ['نعم', 'لا']

# جلب الفئات من قاعدة البيانات
cursor.execute("SELECT parent_category, sub_category FROM categories")
categories = cursor.fetchall()

def random_activity_date():
    date = datetime.now() - timedelta(days=random.randint(0, 365))
    return date.strftime('%Y-%m-%d')

# إضافة بيانات عشوائية
for _ in range(100):
    person = random.choice(people)
    parent_category, sub_category = random.choice(categories)
    activity_name = f"نشاط {random.randint(1, 100)}"
    is_approved = random.choice(is_approved_choices)
    governorate = random.choice(governorates)
    district = random.choice(districts)
    sub_district = random.choice(sub_districts)
    village = random.choice(villages)
    participants = random.randint(10, 100)
    result = random.choice(results)
    recommendation = random.choice(recommendations)
    note = random.choice(notes)
    activity_date = random_activity_date()

    cursor.execute('''
        INSERT INTO activities (
            person, activity_name, parent_category, sub_category, is_approved,
            governorate, district, sub_district, village, participants,
            results, recommendations, notes, activity_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        person, activity_name, parent_category, sub_category, is_approved,
        governorate, district, sub_district, village, participants,
        result, recommendation, note, activity_date
    ))

conn.commit()
conn.close()