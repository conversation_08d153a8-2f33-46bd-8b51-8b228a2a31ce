"""
لوحة المعلومات التفاعلية الشاملة
تعرض مؤشرات الأداء الرئيسية والرسوم البيانية التفاعلية
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QGridLayout, QScrollArea, QGroupBox, QSplitter,
    QComboBox, QDateEdit, QCheckBox, QSpinBox, QProgressBar,
    QTabWidget, QTableWidget, QTableWidgetItem, QHeaderView,
    QTextEdit, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, QTimer, QDate, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter, QBrush
import matplotlib
matplotlib.use('Qt5Agg')

# تعيين الخط العربي
plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class KPICard(QFrame):
    """بطاقة مؤشر أداء رئيسي"""
    
    def __init__(self, title, value, subtitle="", color="#4CAF50", icon="📊"):
        super().__init__()
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.color = color
        self.icon = icon
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                padding: 15px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(5)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon)
        icon_label.setFont(QFont("Arial", 20))
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("color: rgba(255, 255, 255, 0.9);")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        layout.addLayout(header_layout)
        
        # القيمة الرئيسية
        value_label = QLabel(str(self.value))
        value_label.setFont(QFont("Arial", 24, QFont.Bold))
        value_label.setStyleSheet("color: white; margin: 10px 0;")
        value_label.setObjectName('value_label')
        layout.addWidget(value_label)
        
        # العنوان الفرعي
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setFont(QFont("Arial", 10))
            subtitle_label.setStyleSheet("color: rgba(255, 255, 255, 0.8);")
            subtitle_label.setObjectName('subtitle_label')
            layout.addWidget(subtitle_label)
        
        self.setLayout(layout)
        self.setMinimumHeight(120)
        self.setMinimumWidth(200)
        
    def darken_color(self, color, factor=0.8):
        """تغميق اللون"""
        # تنفيذ بسيط
        return color
        
    def lighten_color(self, color, factor=1.2):
        """تفتيح اللون"""
        # تنفيذ بسيط
        return color
        
    def update_value(self, new_value, new_subtitle=""):
        """تحديث قيمة البطاقة"""
        self.value = new_value
        if new_subtitle:
            self.subtitle = new_subtitle

        # تحديث القيم بدلاً من إعادة إنشاء الواجهة
        layout = self.layout()
        if layout:
            # البحث عن label القيمة وتحديثه
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if isinstance(widget, QLabel) and hasattr(widget, 'objectName'):
                        if widget.objectName() == 'value_label':
                            widget.setText(str(new_value))
                        elif widget.objectName() == 'subtitle_label' and new_subtitle:
                            widget.setText(new_subtitle)


class InteractiveChart(QWidget):
    """رسم بياني تفاعلي"""
    
    def __init__(self, chart_type="bar", title="رسم بياني"):
        super().__init__()
        self.chart_type = chart_type
        self.title = title
        self.figure = Figure(figsize=(8, 6))
        self.canvas = FigureCanvas(self.figure)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة الرسم البياني"""
        layout = QVBoxLayout()
        
        # شريط التحكم
        control_layout = QHBoxLayout()
        
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        control_layout.addWidget(title_label)
        
        control_layout.addStretch()
        
        # أزرار التحكم
        refresh_btn = QPushButton("🔄")
        refresh_btn.setMaximumWidth(30)
        refresh_btn.clicked.connect(self.refresh_chart)
        control_layout.addWidget(refresh_btn)
        
        export_btn = QPushButton("💾")
        export_btn.setMaximumWidth(30)
        export_btn.clicked.connect(self.export_chart)
        control_layout.addWidget(export_btn)
        
        layout.addLayout(control_layout)
        layout.addWidget(self.canvas)
        self.setLayout(layout)
        
    def plot_data(self, data, labels=None, colors=None):
        """رسم البيانات"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        if colors is None:
            colors = ['#4CAF50', '#2196F3', '#FF9800', '#f44336', '#9C27B0']
        
        if self.chart_type == "bar":
            bars = ax.bar(range(len(data)), data, color=colors[:len(data)])
            if labels:
                ax.set_xticks(range(len(labels)))
                ax.set_xticklabels(labels, rotation=45, ha='right')
                
        elif self.chart_type == "pie":
            ax.pie(data, labels=labels, autopct='%1.1f%%', colors=colors[:len(data)])
            
        elif self.chart_type == "line":
            ax.plot(range(len(data)), data, marker='o', linewidth=2, color=colors[0])
            if labels:
                ax.set_xticks(range(len(labels)))
                ax.set_xticklabels(labels, rotation=45, ha='right')
        
        ax.set_title(self.title, fontsize=14, fontweight='bold')
        self.figure.tight_layout()
        self.canvas.draw()
        
    def refresh_chart(self):
        """تحديث الرسم البياني"""
        # سيتم تنفيذها في الفئات المشتقة
        pass
        
    def export_chart(self):
        """تصدير الرسم البياني"""
        # سيتم تنفيذها لاحقاً
        pass


class DataRefreshWorker(QThread):
    """عامل تحديث البيانات في الخلفية"""

    data_updated = pyqtSignal(dict)

    def __init__(self, db_path):
        super().__init__()
        self.db_path = db_path
        self.running = True
        
    def run(self):
        """تشغيل عامل التحديث"""
        while self.running:
            try:
                data = self.fetch_dashboard_data()
                self.data_updated.emit(data)
                self.msleep(30000)  # تحديث كل 30 ثانية
            except Exception as e:
                print(f"خطأ في تحديث البيانات: {e}")
                self.msleep(60000)  # إعادة المحاولة بعد دقيقة
                
    def fetch_dashboard_data(self):
        """جلب بيانات لوحة المعلومات"""
        # إنشاء اتصال جديد في هذا الخيط
        import sqlite3
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        data = {}
        
        # الإحصائيات العامة
        cursor.execute("SELECT COUNT(*) FROM activities")
        data['total_activities'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
        data['completed_activities'] = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(participants) FROM activities WHERE participants IS NOT NULL")
        data['total_participants'] = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(budget) FROM activities WHERE budget IS NOT NULL")
        data['total_budget'] = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(actual_cost) FROM activities WHERE actual_cost IS NOT NULL")
        data['total_cost'] = cursor.fetchone()[0] or 0
        
        # إحصائيات التصنيفات
        cursor.execute("""
            SELECT parent_category, COUNT(*), SUM(participants), SUM(budget)
            FROM activities 
            GROUP BY parent_category 
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        data['category_stats'] = cursor.fetchall()
        
        # إحصائيات المحافظات
        cursor.execute("""
            SELECT governorate, COUNT(*), SUM(participants)
            FROM activities 
            WHERE governorate IS NOT NULL AND governorate != ''
            GROUP BY governorate 
            ORDER BY COUNT(*) DESC
            LIMIT 10
        """)
        data['location_stats'] = cursor.fetchall()
        
        # الاتجاه الشهري
        cursor.execute("""
            SELECT strftime('%Y-%m', activity_date) as month, COUNT(*), SUM(participants)
            FROM activities 
            WHERE activity_date IS NOT NULL
            GROUP BY strftime('%Y-%m', activity_date)
            ORDER BY month DESC
            LIMIT 12
        """)
        data['monthly_trend'] = cursor.fetchall()
        
        # أحدث الأنشطة
        cursor.execute("""
            SELECT person, activity_name, parent_category, activity_date, status
            FROM activities 
            ORDER BY created_at DESC
            LIMIT 10
        """)
        data['recent_activities'] = cursor.fetchall()
        
        # أداء المنفذين
        cursor.execute("""
            SELECT person, COUNT(*) as total, 
                   COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) as completed,
                   SUM(participants) as participants
            FROM activities 
            GROUP BY person 
            ORDER BY completed DESC, total DESC
            LIMIT 10
        """)
        data['person_performance'] = cursor.fetchall()

        conn.close()
        return data
        
    def stop(self):
        """إيقاف عامل التحديث"""
        self.running = False


class DashboardWidget(QWidget):
    """لوحة المعلومات الرئيسية"""
    
    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.kpi_cards = {}
        self.charts = {}
        self.data_worker = None
        self.setup_ui()
        self.start_data_refresh()
        
    def setup_ui(self):
        """إعداد واجهة لوحة المعلومات"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("📊 لوحة المعلومات التفاعلية")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1976D2, stop:1 #1565C0);
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        # مؤشرات الأداء الرئيسية
        kpi_section = self.create_kpi_section()
        scroll_layout.addWidget(kpi_section)
        
        # الرسوم البيانية
        charts_section = self.create_charts_section()
        scroll_layout.addWidget(charts_section)
        
        # الجداول والقوائم
        tables_section = self.create_tables_section()
        scroll_layout.addWidget(tables_section)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        self.setLayout(layout)
        
    def create_kpi_section(self):
        """إنشاء قسم مؤشرات الأداء الرئيسية"""
        section = QGroupBox("مؤشرات الأداء الرئيسية")
        layout = QGridLayout()
        
        # إنشاء بطاقات المؤشرات
        self.kpi_cards['total_activities'] = KPICard(
            "إجمالي الأنشطة", "0", "جميع الأنشطة المسجلة", "#4CAF50", "📋"
        )
        layout.addWidget(self.kpi_cards['total_activities'], 0, 0)
        
        self.kpi_cards['completed_activities'] = KPICard(
            "الأنشطة المكتملة", "0", "الأنشطة المنجزة", "#2196F3", "✅"
        )
        layout.addWidget(self.kpi_cards['completed_activities'], 0, 1)
        
        self.kpi_cards['total_participants'] = KPICard(
            "إجمالي المشاركين", "0", "جميع المشاركين", "#FF9800", "👥"
        )
        layout.addWidget(self.kpi_cards['total_participants'], 0, 2)
        
        self.kpi_cards['completion_rate'] = KPICard(
            "معدل الإنجاز", "0%", "نسبة الأنشطة المكتملة", "#9C27B0", "📈"
        )
        layout.addWidget(self.kpi_cards['completion_rate'], 0, 3)
        
        self.kpi_cards['total_budget'] = KPICard(
            "إجمالي الميزانية", "0", "الميزانية الكلية", "#f44336", "💰"
        )
        layout.addWidget(self.kpi_cards['total_budget'], 1, 0)
        
        self.kpi_cards['budget_efficiency'] = KPICard(
            "كفاءة الميزانية", "0%", "نسبة الوفورات", "#607D8B", "💡"
        )
        layout.addWidget(self.kpi_cards['budget_efficiency'], 1, 1)
        
        section.setLayout(layout)
        return section
        
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        section = QGroupBox("الرسوم البيانية التفاعلية")
        layout = QGridLayout()
        
        # رسم بياني للتصنيفات
        self.charts['categories'] = InteractiveChart("bar", "توزيع الأنشطة حسب التصنيف")
        layout.addWidget(self.charts['categories'], 0, 0)
        
        # رسم بياني للمحافظات
        self.charts['locations'] = InteractiveChart("pie", "توزيع الأنشطة حسب المحافظة")
        layout.addWidget(self.charts['locations'], 0, 1)
        
        # رسم بياني للاتجاه الشهري
        self.charts['monthly_trend'] = InteractiveChart("line", "الاتجاه الشهري للأنشطة")
        layout.addWidget(self.charts['monthly_trend'], 1, 0, 1, 2)
        
        section.setLayout(layout)
        return section

    def create_tables_section(self):
        """إنشاء قسم الجداول والقوائم"""
        section = QGroupBox("البيانات التفصيلية")
        layout = QHBoxLayout()

        # جدول أحدث الأنشطة
        recent_group = QGroupBox("أحدث الأنشطة")
        recent_layout = QVBoxLayout()

        self.recent_activities_table = QTableWidget()
        self.recent_activities_table.setColumnCount(5)
        self.recent_activities_table.setHorizontalHeaderLabels([
            "الشخص", "النشاط", "التصنيف", "التاريخ", "الحالة"
        ])
        self.recent_activities_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.recent_activities_table.setMaximumHeight(200)
        recent_layout.addWidget(self.recent_activities_table)

        recent_group.setLayout(recent_layout)
        layout.addWidget(recent_group)

        # قائمة أداء المنفذين
        performance_group = QGroupBox("أداء المنفذين")
        performance_layout = QVBoxLayout()

        self.performance_list = QListWidget()
        self.performance_list.setMaximumHeight(200)
        performance_layout.addWidget(self.performance_list)

        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)

        section.setLayout(layout)
        return section

    def start_data_refresh(self):
        """بدء تحديث البيانات التلقائي"""
        # تمرير مسار قاعدة البيانات بدلاً من الاتصال
        self.data_worker = DataRefreshWorker("activities.db")
        self.data_worker.data_updated.connect(self.update_dashboard)
        self.data_worker.start()

        # تحديث فوري
        self.update_dashboard_now()

    def update_dashboard_now(self):
        """تحديث لوحة المعلومات فوراً"""
        try:
            cursor = self.conn.cursor()

            # تحديث مؤشرات الأداء الرئيسية
            cursor.execute("SELECT COUNT(*) FROM activities")
            total_activities = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
            completed_activities = cursor.fetchone()[0]

            cursor.execute("SELECT SUM(participants) FROM activities WHERE participants IS NOT NULL")
            total_participants = cursor.fetchone()[0] or 0

            cursor.execute("SELECT SUM(budget) FROM activities WHERE budget IS NOT NULL")
            total_budget = cursor.fetchone()[0] or 0

            cursor.execute("SELECT SUM(actual_cost) FROM activities WHERE actual_cost IS NOT NULL")
            total_cost = cursor.fetchone()[0] or 0

            # حساب المعدلات
            completion_rate = (completed_activities / total_activities * 100) if total_activities > 0 else 0
            budget_efficiency = ((total_budget - total_cost) / total_budget * 100) if total_budget > 0 else 0

            # تحديث البطاقات
            self.kpi_cards['total_activities'].update_value(total_activities)
            self.kpi_cards['completed_activities'].update_value(completed_activities)
            self.kpi_cards['total_participants'].update_value(f"{total_participants:,}")
            self.kpi_cards['completion_rate'].update_value(f"{completion_rate:.1f}%")
            self.kpi_cards['total_budget'].update_value(f"{total_budget:,.0f}")
            self.kpi_cards['budget_efficiency'].update_value(f"{budget_efficiency:.1f}%")

            # تحديث الرسوم البيانية
            self.update_charts()

            # تحديث الجداول
            self.update_tables()

        except Exception as e:
            print(f"خطأ في تحديث لوحة المعلومات: {e}")

    def update_dashboard(self, data):
        """تحديث لوحة المعلومات بالبيانات الجديدة"""
        try:
            # تحديث مؤشرات الأداء الرئيسية
            total_activities = data['total_activities']
            completed_activities = data['completed_activities']
            total_participants = data['total_participants']
            total_budget = data['total_budget']
            total_cost = data['total_cost']

            completion_rate = (completed_activities / total_activities * 100) if total_activities > 0 else 0
            budget_efficiency = ((total_budget - total_cost) / total_budget * 100) if total_budget > 0 else 0

            self.kpi_cards['total_activities'].update_value(total_activities)
            self.kpi_cards['completed_activities'].update_value(completed_activities)
            self.kpi_cards['total_participants'].update_value(f"{total_participants:,}")
            self.kpi_cards['completion_rate'].update_value(f"{completion_rate:.1f}%")
            self.kpi_cards['total_budget'].update_value(f"{total_budget:,.0f}")
            self.kpi_cards['budget_efficiency'].update_value(f"{budget_efficiency:.1f}%")

            # تحديث الرسوم البيانية بالبيانات الجديدة
            self.update_charts_with_data(data)

            # تحديث الجداول بالبيانات الجديدة
            self.update_tables_with_data(data)

        except Exception as e:
            print(f"خطأ في تحديث لوحة المعلومات: {e}")

    def update_charts(self):
        """تحديث الرسوم البيانية"""
        try:
            cursor = self.conn.cursor()

            # رسم بياني للتصنيفات
            cursor.execute("""
                SELECT parent_category, COUNT(*)
                FROM activities
                GROUP BY parent_category
                ORDER BY COUNT(*) DESC
                LIMIT 8
            """)
            category_data = cursor.fetchall()

            if category_data:
                categories = [row[0] for row in category_data]
                counts = [row[1] for row in category_data]
                self.charts['categories'].plot_data(counts, categories)

            # رسم بياني للمحافظات
            cursor.execute("""
                SELECT governorate, COUNT(*)
                FROM activities
                WHERE governorate IS NOT NULL AND governorate != ''
                GROUP BY governorate
                ORDER BY COUNT(*) DESC
                LIMIT 6
            """)
            location_data = cursor.fetchall()

            if location_data:
                locations = [row[0] for row in location_data]
                counts = [row[1] for row in location_data]
                self.charts['locations'].plot_data(counts, locations)

            # رسم بياني للاتجاه الشهري
            cursor.execute("""
                SELECT strftime('%Y-%m', activity_date) as month, COUNT(*)
                FROM activities
                WHERE activity_date IS NOT NULL
                GROUP BY strftime('%Y-%m', activity_date)
                ORDER BY month DESC
                LIMIT 12
            """)
            monthly_data = cursor.fetchall()

            if monthly_data:
                months = [row[0] for row in reversed(monthly_data)]
                counts = [row[1] for row in reversed(monthly_data)]
                self.charts['monthly_trend'].plot_data(counts, months)

        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")

    def update_charts_with_data(self, data):
        """تحديث الرسوم البيانية بالبيانات المرسلة"""
        try:
            # رسم بياني للتصنيفات
            if data['category_stats']:
                categories = [row[0] for row in data['category_stats'][:8]]
                counts = [row[1] for row in data['category_stats'][:8]]
                self.charts['categories'].plot_data(counts, categories)

            # رسم بياني للمحافظات
            if data['location_stats']:
                locations = [row[0] for row in data['location_stats'][:6]]
                counts = [row[1] for row in data['location_stats'][:6]]
                self.charts['locations'].plot_data(counts, locations)

            # رسم بياني للاتجاه الشهري
            if data['monthly_trend']:
                months = [row[0] for row in reversed(data['monthly_trend'])]
                counts = [row[1] for row in reversed(data['monthly_trend'])]
                self.charts['monthly_trend'].plot_data(counts, months)

        except Exception as e:
            print(f"خطأ في تحديث الرسوم البيانية: {e}")

    def update_tables(self):
        """تحديث الجداول"""
        try:
            cursor = self.conn.cursor()

            # تحديث جدول أحدث الأنشطة
            cursor.execute("""
                SELECT person, activity_name, parent_category, activity_date, status
                FROM activities
                ORDER BY created_at DESC
                LIMIT 10
            """)
            recent_activities = cursor.fetchall()

            self.recent_activities_table.setRowCount(len(recent_activities))
            for row_idx, activity in enumerate(recent_activities):
                for col_idx, value in enumerate(activity):
                    item = QTableWidgetItem(str(value) if value else "")
                    self.recent_activities_table.setItem(row_idx, col_idx, item)

            # تحديث قائمة أداء المنفذين
            cursor.execute("""
                SELECT person, COUNT(*) as total,
                       COUNT(CASE WHEN status = 'مكتمل' THEN 1 END) as completed,
                       SUM(participants) as participants
                FROM activities
                GROUP BY person
                ORDER BY completed DESC, total DESC
                LIMIT 10
            """)
            performance_data = cursor.fetchall()

            self.performance_list.clear()
            for person, total, completed, participants in performance_data:
                completion_rate = (completed / total * 100) if total > 0 else 0
                item_text = f"{person}: {completed}/{total} ({completion_rate:.1f}%) - {participants or 0} مشارك"
                item = QListWidgetItem(item_text)
                self.performance_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحديث الجداول: {e}")

    def update_tables_with_data(self, data):
        """تحديث الجداول بالبيانات المرسلة"""
        try:
            # تحديث جدول أحدث الأنشطة
            if data['recent_activities']:
                recent_activities = data['recent_activities']
                self.recent_activities_table.setRowCount(len(recent_activities))
                for row_idx, activity in enumerate(recent_activities):
                    for col_idx, value in enumerate(activity):
                        item = QTableWidgetItem(str(value) if value else "")
                        self.recent_activities_table.setItem(row_idx, col_idx, item)

            # تحديث قائمة أداء المنفذين
            if data['person_performance']:
                self.performance_list.clear()
                for person, total, completed, participants in data['person_performance']:
                    completion_rate = (completed / total * 100) if total > 0 else 0
                    item_text = f"{person}: {completed}/{total} ({completion_rate:.1f}%) - {participants or 0} مشارك"
                    item = QListWidgetItem(item_text)
                    self.performance_list.addItem(item)

        except Exception as e:
            print(f"خطأ في تحديث الجداول: {e}")

    def closeEvent(self, event):
        """إيقاف عامل التحديث عند إغلاق النافذة"""
        if self.data_worker:
            self.data_worker.stop()
            self.data_worker.wait()
        event.accept()
