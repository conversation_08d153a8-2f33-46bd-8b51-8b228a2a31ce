#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة الأنشطة المتقدم
يتضمن معالجة الأخطاء وتثبيت المتطلبات
"""

import sys
import os
import subprocess

def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    try:
        import PyQt5
        import matplotlib
        import pandas
        import openpyxl
        import reportlab
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("🔄 جاري تثبيت المتطلبات...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت المتطلبات")
            print("يرجى تثبيت المتطلبات يدوياً:")
            print("pip install PyQt5 matplotlib pandas openpyxl reportlab psutil schedule")
            return False

def main():
    """الدالة الرئيسية"""
    print("🏛️ نظام إدارة الأنشطة المتقدم")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not install_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    try:
        print("🚀 بدء تشغيل التطبيق...")
        
        # استيراد التطبيق الرئيسي
        from ac import EnhancedActivityTracker
        from PyQt5.QtWidgets import QApplication
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("نظام إدارة الأنشطة المتقدم")
        app.setApplicationVersion("2.0")
        
        # إنشاء النافذة الرئيسية
        window = EnhancedActivityTracker()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح")
        
        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\nتفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
