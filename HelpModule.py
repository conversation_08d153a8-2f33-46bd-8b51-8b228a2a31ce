"""
وحدة المساعدة والتوثيق التفاعلية
تتضمن دليل المستخدم الشامل ونظام المساعدة التفاعلي
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QTreeWidget, QTreeWidgetItem, QSplitter,
    QTabWidget, QGroupBox, QFormLayout, QLineEdit,
    QScrollArea, QFrame, QListWidget, QListWidgetItem,
    QDialog, QDialogButtonBox, QComboBox, QCheckBox
)
from PyQt5.QtCore import Qt, QUrl, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QDesktopServices
import json
import os
from datetime import datetime

class HelpContent:
    """محتوى المساعدة والتوثيق"""
    
    def __init__(self):
        self.help_data = self.load_help_content()
        
    def load_help_content(self):
        """تحميل محتوى المساعدة"""
        return {
            "getting_started": {
                "title": "البدء السريع",
                "icon": "🚀",
                "content": """
                <h2>مرحباً بك في نظام إدارة الأنشطة المتقدم</h2>
                
                <h3>الخطوات الأولى:</h3>
                <ol>
                    <li><strong>إضافة نشاط جديد:</strong> انقر على زر "إضافة نشاط" في التبويب الرئيسي</li>
                    <li><strong>عرض البيانات:</strong> استخدم تبويب "عرض البيانات" لاستعراض جميع الأنشطة</li>
                    <li><strong>إنتاج التقارير:</strong> انتقل إلى تبويب "التقارير المتقدمة" لإنشاء تقارير مفصلة</li>
                    <li><strong>مراقبة الأداء:</strong> استخدم "لوحة المعلومات" لمتابعة الإحصائيات الحية</li>
                </ol>
                
                <h3>نصائح مهمة:</h3>
                <ul>
                    <li>احرص على إنشاء نسخ احتياطية دورية من بياناتك</li>
                    <li>استخدم الفلاتر لتسهيل البحث في البيانات</li>
                    <li>راجع سجل الأخطاء في حالة مواجهة مشاكل</li>
                </ul>
                """
            },
            "activities_management": {
                "title": "إدارة الأنشطة",
                "icon": "📋",
                "content": """
                <h2>إدارة الأنشطة</h2>
                
                <h3>إضافة نشاط جديد:</h3>
                <ol>
                    <li>انقر على زر "إضافة نشاط" في الشريط العلوي</li>
                    <li>املأ الحقول المطلوبة (مميزة بعلامة *)</li>
                    <li>اختر التصنيف المناسب من القائمة المنسدلة</li>
                    <li>حدد الموقع الجغرافي للنشاط</li>
                    <li>أدخل عدد المشاركين والميزانية</li>
                    <li>انقر على "حفظ" لإضافة النشاط</li>
                </ol>
                
                <h3>تعديل نشاط موجود:</h3>
                <ol>
                    <li>حدد النشاط من جدول البيانات</li>
                    <li>انقر على زر "تعديل"</li>
                    <li>قم بالتعديلات المطلوبة</li>
                    <li>احفظ التغييرات</li>
                </ol>
                
                <h3>حذف نشاط:</h3>
                <ol>
                    <li>حدد النشاط المراد حذفه</li>
                    <li>انقر على زر "حذف"</li>
                    <li>أكد عملية الحذف</li>
                </ol>
                
                <div style="background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <strong>تنبيه:</strong> عملية الحذف نهائية ولا يمكن التراجع عنها إلا من خلال النسخ الاحتياطية.
                </div>
                """
            },
            "reports": {
                "title": "التقارير والإحصائيات",
                "icon": "📊",
                "content": """
                <h2>التقارير والإحصائيات</h2>
                
                <h3>أنواع التقارير المتاحة:</h3>
                <ul>
                    <li><strong>تقرير ملخص:</strong> نظرة عامة على جميع الأنشطة</li>
                    <li><strong>تقرير مفصل:</strong> بيانات تفصيلية مع إمكانية التفلتر</li>
                    <li><strong>تقرير الأداء:</strong> مؤشرات الأداء الرئيسية</li>
                    <li><strong>تقرير جغرافي:</strong> توزيع الأنشطة حسب المناطق</li>
                    <li><strong>تقرير مالي:</strong> تحليل الميزانيات والتكاليف</li>
                </ul>
                
                <h3>إنتاج تقرير:</h3>
                <ol>
                    <li>انتقل إلى تبويب "التقارير المتقدمة"</li>
                    <li>اختر نوع التقرير المطلوب</li>
                    <li>حدد الفلاتر (التاريخ، التصنيف، المنطقة)</li>
                    <li>اختر الخيارات الإضافية (رسوم بيانية، إحصائيات)</li>
                    <li>انقر على "إنتاج التقرير"</li>
                </ol>
                
                <h3>تصدير التقارير:</h3>
                <p>يمكن تصدير التقارير بعدة تنسيقات:</p>
                <ul>
                    <li><strong>Excel:</strong> للتحليل المتقدم</li>
                    <li><strong>PDF:</strong> للطباعة والمشاركة</li>
                    <li><strong>Word:</strong> للتحرير والتخصيص</li>
                    <li><strong>CSV:</strong> للاستيراد في برامج أخرى</li>
                </ul>
                """
            },
            "import_export": {
                "title": "الاستيراد والتصدير",
                "icon": "🔄",
                "content": """
                <h2>الاستيراد والتصدير</h2>
                
                <h3>استيراد البيانات:</h3>
                <ol>
                    <li>انتقل إلى تبويب "الاستيراد الذكي"</li>
                    <li>اختر ملف البيانات (Excel, CSV, JSON)</li>
                    <li>راجع معاينة البيانات</li>
                    <li>تأكد من تطابق الأعمدة</li>
                    <li>شغل التحقق من صحة البيانات</li>
                    <li>ابدأ عملية الاستيراد</li>
                </ol>
                
                <h3>تصدير البيانات:</h3>
                <ol>
                    <li>انتقل إلى تبويب "التصدير المتقدم"</li>
                    <li>اختر نوع التصدير (سريع أو مخصص)</li>
                    <li>حدد التنسيق المطلوب</li>
                    <li>اختر الفلاتر والخيارات</li>
                    <li>ابدأ عملية التصدير</li>
                </ol>
                
                <h3>التنسيقات المدعومة:</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <th>التنسيق</th>
                        <th>الاستيراد</th>
                        <th>التصدير</th>
                        <th>الاستخدام</th>
                    </tr>
                    <tr>
                        <td>Excel (.xlsx)</td>
                        <td>✅</td>
                        <td>✅</td>
                        <td>التحليل والتقارير</td>
                    </tr>
                    <tr>
                        <td>CSV (.csv)</td>
                        <td>✅</td>
                        <td>✅</td>
                        <td>التبادل مع برامج أخرى</td>
                    </tr>
                    <tr>
                        <td>JSON (.json)</td>
                        <td>✅</td>
                        <td>✅</td>
                        <td>النسخ الاحتياطية</td>
                    </tr>
                    <tr>
                        <td>PDF (.pdf)</td>
                        <td>❌</td>
                        <td>✅</td>
                        <td>الطباعة والمشاركة</td>
                    </tr>
                </table>
                """
            },
            "backup_restore": {
                "title": "النسخ الاحتياطي والاستعادة",
                "icon": "💾",
                "content": """
                <h2>النسخ الاحتياطي والاستعادة</h2>
                
                <h3>أنواع النسخ الاحتياطية:</h3>
                <ul>
                    <li><strong>نسخة كاملة:</strong> تحتوي على جميع البيانات</li>
                    <li><strong>نسخة تزايدية:</strong> تحتوي على التغييرات منذ آخر نسخة</li>
                    <li><strong>نسخة تفاضلية:</strong> تحتوي على التغييرات منذ آخر نسخة كاملة</li>
                </ul>
                
                <h3>إنشاء نسخة احتياطية:</h3>
                <ol>
                    <li>انتقل إلى تبويب "النسخ الاحتياطي"</li>
                    <li>اختر نوع النسخة المطلوبة</li>
                    <li>حدد مسار الحفظ</li>
                    <li>اختر الخيارات الإضافية (ضغط، تشفير)</li>
                    <li>انقر على "بدء النسخ الاحتياطي"</li>
                </ol>
                
                <h3>استعادة البيانات:</h3>
                <ol>
                    <li>انتقل إلى تبويب "الاستعادة"</li>
                    <li>اختر ملف النسخة الاحتياطية</li>
                    <li>راجع معلومات النسخة</li>
                    <li>اختر وضع الاستعادة</li>
                    <li>انقر على "بدء الاستعادة"</li>
                </ol>
                
                <div style="background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <strong>نصيحة:</strong> فعل النسخ الاحتياطي التلقائي لضمان حماية بياناتك.
                </div>
                """
            },
            "troubleshooting": {
                "title": "حل المشاكل",
                "icon": "🔧",
                "content": """
                <h2>حل المشاكل الشائعة</h2>
                
                <h3>مشاكل الأداء:</h3>
                <ul>
                    <li><strong>بطء في التطبيق:</strong> استخدم أدوات تحسين قاعدة البيانات</li>
                    <li><strong>استهلاك عالي للذاكرة:</strong> شغل تنظيف الذاكرة التلقائي</li>
                    <li><strong>تجمد الواجهة:</strong> أعد تشغيل التطبيق وتحقق من السجلات</li>
                </ul>
                
                <h3>مشاكل البيانات:</h3>
                <ul>
                    <li><strong>فقدان البيانات:</strong> استعد من آخر نسخة احتياطية</li>
                    <li><strong>أخطاء في الاستيراد:</strong> تحقق من تنسيق الملف وصحة البيانات</li>
                    <li><strong>تضارب في البيانات:</strong> استخدم أدوات التحقق من سلامة قاعدة البيانات</li>
                </ul>
                
                <h3>مشاكل التصدير:</h3>
                <ul>
                    <li><strong>فشل في التصدير:</strong> تحقق من مساحة القرص والصلاحيات</li>
                    <li><strong>ملفات تالفة:</strong> جرب تنسيق تصدير مختلف</li>
                    <li><strong>بيانات ناقصة:</strong> راجع الفلاتر المطبقة</li>
                </ul>
                
                <h3>الحصول على المساعدة:</h3>
                <ol>
                    <li>راجع سجل الأخطاء في تبويب "مراقبة الأداء"</li>
                    <li>استخدم أدوات التشخيص المدمجة</li>
                    <li>أنشئ نسخة احتياطية قبل أي إصلاحات</li>
                    <li>اتصل بالدعم الفني مع تفاصيل المشكلة</li>
                </ol>
                """
            },
            "keyboard_shortcuts": {
                "title": "اختصارات لوحة المفاتيح",
                "icon": "⌨️",
                "content": """
                <h2>اختصارات لوحة المفاتيح</h2>
                
                <h3>اختصارات عامة:</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <th>الاختصار</th>
                        <th>الوظيفة</th>
                    </tr>
                    <tr>
                        <td>Ctrl + N</td>
                        <td>إضافة نشاط جديد</td>
                    </tr>
                    <tr>
                        <td>Ctrl + S</td>
                        <td>حفظ</td>
                    </tr>
                    <tr>
                        <td>Ctrl + F</td>
                        <td>بحث</td>
                    </tr>
                    <tr>
                        <td>Ctrl + P</td>
                        <td>طباعة</td>
                    </tr>
                    <tr>
                        <td>F5</td>
                        <td>تحديث</td>
                    </tr>
                    <tr>
                        <td>F1</td>
                        <td>المساعدة</td>
                    </tr>
                </table>
                
                <h3>اختصارات التنقل:</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <th>الاختصار</th>
                        <th>الوظيفة</th>
                    </tr>
                    <tr>
                        <td>Ctrl + Tab</td>
                        <td>التنقل بين التبويبات</td>
                    </tr>
                    <tr>
                        <td>Ctrl + 1-9</td>
                        <td>الانتقال إلى تبويب محدد</td>
                    </tr>
                    <tr>
                        <td>Alt + F4</td>
                        <td>إغلاق التطبيق</td>
                    </tr>
                </table>
                """
            }
        }
    
    def get_content(self, section_id):
        """الحصول على محتوى قسم معين"""
        return self.help_data.get(section_id, {})
    
    def search_content(self, query):
        """البحث في محتوى المساعدة"""
        results = []
        query = query.lower()
        
        for section_id, section_data in self.help_data.items():
            title = section_data.get('title', '')
            content = section_data.get('content', '')
            
            if query in title.lower() or query in content.lower():
                results.append({
                    'id': section_id,
                    'title': title,
                    'icon': section_data.get('icon', '📄'),
                    'relevance': self.calculate_relevance(query, title, content)
                })
        
        # ترتيب النتائج حسب الصلة
        results.sort(key=lambda x: x['relevance'], reverse=True)
        return results
    
    def calculate_relevance(self, query, title, content):
        """حساب مدى صلة النتيجة بالبحث"""
        relevance = 0
        
        # وزن أكبر للعنوان
        if query in title.lower():
            relevance += 10
        
        # وزن للمحتوى
        content_lower = content.lower()
        relevance += content_lower.count(query)
        
        return relevance


class HelpDialog(QDialog):
    """نافذة المساعدة المنبثقة"""
    
    def __init__(self, section_id=None, parent=None):
        super().__init__(parent)
        self.help_content = HelpContent()
        self.section_id = section_id
        self.setup_ui()
        
        if section_id:
            self.show_section(section_id)
            
    def setup_ui(self):
        """إعداد واجهة نافذة المساعدة"""
        self.setWindowTitle("المساعدة والتوثيق")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout()
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث في المساعدة...")
        self.search_input.returnPressed.connect(self.search_help)
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_help)
        search_layout.addWidget(search_btn)
        
        layout.addLayout(search_layout)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة المواضيع
        self.topics_tree = QTreeWidget()
        self.topics_tree.setHeaderLabel("المواضيع")
        self.topics_tree.itemClicked.connect(self.topic_selected)
        self.populate_topics()
        splitter.addWidget(self.topics_tree)
        
        # منطقة عرض المحتوى
        self.content_display = QTextEdit()
        self.content_display.setReadOnly(True)
        splitter.addWidget(self.content_display)
        
        splitter.setSizes([250, 550])
        layout.addWidget(splitter)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Close)
        buttons.rejected.connect(self.close)
        layout.addWidget(buttons)
        
        self.setLayout(layout)
        
    def populate_topics(self):
        """تعبئة قائمة المواضيع"""
        for section_id, section_data in self.help_content.help_data.items():
            item = QTreeWidgetItem()
            item.setText(0, f"{section_data['icon']} {section_data['title']}")
            item.setData(0, Qt.UserRole, section_id)
            self.topics_tree.addTopLevelItem(item)
            
    def topic_selected(self, item):
        """عند اختيار موضوع"""
        section_id = item.data(0, Qt.UserRole)
        if section_id:
            self.show_section(section_id)
            
    def show_section(self, section_id):
        """عرض قسم معين"""
        section_data = self.help_content.get_content(section_id)
        if section_data:
            self.content_display.setHtml(section_data.get('content', ''))
            
    def search_help(self):
        """البحث في المساعدة"""
        query = self.search_input.text().strip()
        if not query:
            return
            
        results = self.help_content.search_content(query)
        
        if results:
            # عرض أول نتيجة
            first_result = results[0]
            self.show_section(first_result['id'])
            
            # تحديد العنصر في القائمة
            for i in range(self.topics_tree.topLevelItemCount()):
                item = self.topics_tree.topLevelItem(i)
                if item.data(0, Qt.UserRole) == first_result['id']:
                    self.topics_tree.setCurrentItem(item)
                    break
        else:
            self.content_display.setHtml("<h2>لم يتم العثور على نتائج</h2><p>جرب كلمات بحث أخرى.</p>")


class HelpWidget(QWidget):
    """واجهة المساعدة الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.help_content = HelpContent()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المساعدة"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("❓ المساعدة والتوثيق")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9C27B0, stop:1 #7B1FA2);
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # تبويبات المساعدة
        tab_widget = QTabWidget()
        
        # تبويب الدليل السريع
        quick_guide_tab = self.create_quick_guide_tab()
        tab_widget.addTab(quick_guide_tab, "الدليل السريع")
        
        # تبويب الأسئلة الشائعة
        faq_tab = self.create_faq_tab()
        tab_widget.addTab(faq_tab, "الأسئلة الشائعة")
        
        # تبويب معلومات التطبيق
        about_tab = self.create_about_tab()
        tab_widget.addTab(about_tab, "حول التطبيق")
        
        layout.addWidget(tab_widget)
        self.setLayout(layout)
        
    def create_quick_guide_tab(self):
        """إنشاء تبويب الدليل السريع"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # شريط البحث
        search_group = QGroupBox("البحث في المساعدة")
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن موضوع...")
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_and_show_help)
        search_layout.addWidget(search_btn)
        
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
        # المواضيع السريعة
        topics_group = QGroupBox("المواضيع الرئيسية")
        topics_layout = QVBoxLayout()
        
        # إنشاء أزرار للمواضيع الرئيسية
        main_topics = [
            ("getting_started", "🚀 البدء السريع"),
            ("activities_management", "📋 إدارة الأنشطة"),
            ("reports", "📊 التقارير والإحصائيات"),
            ("import_export", "🔄 الاستيراد والتصدير"),
            ("backup_restore", "💾 النسخ الاحتياطي"),
            ("troubleshooting", "🔧 حل المشاكل")
        ]
        
        for section_id, title in main_topics:
            btn = QPushButton(title)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #f8f9fa;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #e9ecef;
                    border-color: #adb5bd;
                }
            """)
            btn.clicked.connect(lambda checked, sid=section_id: self.show_help_dialog(sid))
            topics_layout.addWidget(btn)
            
        topics_group.setLayout(topics_layout)
        layout.addWidget(topics_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_faq_tab(self):
        """إنشاء تبويب الأسئلة الشائعة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # قائمة الأسئلة الشائعة
        faq_data = [
            {
                "question": "كيف أقوم بإنشاء نسخة احتياطية من بياناتي؟",
                "answer": "انتقل إلى تبويب 'النسخ الاحتياطي' واختر 'نسخ احتياطي سريع' للحصول على نسخة كاملة فورية، أو استخدم 'نسخ احتياطي مخصص' لمزيد من الخيارات."
            },
            {
                "question": "ماذا أفعل إذا فقدت بياناتي؟",
                "answer": "استخدم ميزة الاستعادة في تبويب 'النسخ الاحتياطي'. اختر ملف النسخة الاحتياطية واتبع التعليمات لاستعادة بياناتك."
            },
            {
                "question": "كيف أقوم بتصدير البيانات إلى Excel؟",
                "answer": "انتقل إلى تبويب 'التصدير المتقدم'، اختر 'تصدير سريع'، ثم حدد تنسيق Excel (.xlsx) وانقر على 'تصدير'."
            },
            {
                "question": "لماذا يعمل التطبيق ببطء؟",
                "answer": "قد يكون السبب كثرة البيانات أو حاجة قاعدة البيانات للتحسين. استخدم أدوات التحسين في تبويب 'مراقبة الأداء'."
            },
            {
                "question": "كيف أقوم بإضافة نشاط جديد؟",
                "answer": "انقر على زر 'إضافة نشاط' في الشريط العلوي، املأ الحقول المطلوبة، ثم انقر على 'حفظ'."
            },
            {
                "question": "هل يمكنني استيراد بيانات من ملف Excel؟",
                "answer": "نعم، انتقل إلى تبويب 'الاستيراد الذكي'، اختر ملف Excel، وتابع خطوات الاستيراد مع التحقق من صحة البيانات."
            },
            {
                "question": "كيف أقوم بإنتاج تقرير مفصل؟",
                "answer": "انتقل إلى تبويب 'التقارير المتقدمة'، اختر 'تقرير مفصل'، حدد الفلاتر المطلوبة، ثم انقر على 'إنتاج التقرير'."
            },
            {
                "question": "ما هي أنواع النسخ الاحتياطية المتاحة؟",
                "answer": "يوفر التطبيق ثلاثة أنواع: النسخة الكاملة (جميع البيانات)، التزايدية (التغييرات الجديدة فقط)، والتفاضلية (التغييرات منذ آخر نسخة كاملة)."
            }
        ]

        # إنشاء قائمة الأسئلة
        faq_list = QListWidget()

        for i, faq in enumerate(faq_data):
            item = QListWidgetItem(f"❓ {faq['question']}")
            item.setData(Qt.UserRole, faq['answer'])
            faq_list.addItem(item)

        faq_list.itemClicked.connect(self.show_faq_answer)
        layout.addWidget(QLabel("الأسئلة الشائعة:"))
        layout.addWidget(faq_list)

        # منطقة عرض الإجابة
        self.faq_answer = QTextEdit()
        self.faq_answer.setReadOnly(True)
        self.faq_answer.setMaximumHeight(150)
        self.faq_answer.setPlaceholderText("اختر سؤالاً لعرض الإجابة...")
        layout.addWidget(QLabel("الإجابة:"))
        layout.addWidget(self.faq_answer)

        widget.setLayout(layout)
        return widget

    def create_about_tab(self):
        """إنشاء تبويب معلومات التطبيق"""
        widget = QWidget()
        layout = QVBoxLayout()

        # معلومات التطبيق
        about_info = QTextEdit()
        about_info.setReadOnly(True)
        about_info.setHtml("""
        <div style="text-align: center; padding: 20px;">
            <h1>🏛️ نظام إدارة الأنشطة المتقدم</h1>
            <h2>الإصدار 2.0</h2>

            <p><strong>تطوير:</strong> فريق التطوير المتخصص</p>
            <p><strong>تاريخ الإصدار:</strong> ديسمبر 2024</p>

            <h3>الميزات الرئيسية:</h3>
            <ul style="text-align: right;">
                <li>إدارة شاملة للأنشطة والفعاليات</li>
                <li>تقارير متقدمة وإحصائيات تفاعلية</li>
                <li>لوحة معلومات ديناميكية</li>
                <li>نظام نسخ احتياطي متطور</li>
                <li>استيراد وتصدير ذكي</li>
                <li>مراقبة الأداء والتحسين</li>
                <li>واجهة مستخدم عصرية وسهلة الاستخدام</li>
            </ul>

            <h3>التقنيات المستخدمة:</h3>
            <ul style="text-align: right;">
                <li>Python 3.8+</li>
                <li>PyQt5 للواجهة الرسومية</li>
                <li>SQLite لقاعدة البيانات</li>
                <li>Matplotlib للرسوم البيانية</li>
                <li>Pandas لمعالجة البيانات</li>
                <li>ReportLab لإنتاج PDF</li>
            </ul>

            <h3>متطلبات النظام:</h3>
            <ul style="text-align: right;">
                <li>نظام التشغيل: Windows 10/11, macOS 10.14+, Linux</li>
                <li>الذاكرة: 4 GB RAM (8 GB مستحسن)</li>
                <li>مساحة القرص: 500 MB</li>
                <li>Python 3.8 أو أحدث</li>
            </ul>

            <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3>🎯 رؤيتنا</h3>
                <p>نسعى لتوفير أفضل الحلول التقنية لإدارة الأنشطة والفعاليات بكفاءة وفعالية عالية.</p>
            </div>

            <div style="background-color: #f3e5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3>📞 الدعم الفني</h3>
                <p>للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل معنا عبر:</p>
                <p>📧 البريد الإلكتروني: <EMAIL></p>
                <p>📱 الهاتف: +966-11-1234567</p>
            </div>

            <p style="margin-top: 30px; color: #666;">
                © 2024 جميع الحقوق محفوظة - نظام إدارة الأنشطة المتقدم
            </p>
        </div>
        """)

        layout.addWidget(about_info)

        # أزرار إضافية
        buttons_layout = QHBoxLayout()

        check_updates_btn = QPushButton("🔄 فحص التحديثات")
        check_updates_btn.clicked.connect(self.check_for_updates)
        buttons_layout.addWidget(check_updates_btn)

        system_info_btn = QPushButton("💻 معلومات النظام")
        system_info_btn.clicked.connect(self.show_system_info)
        buttons_layout.addWidget(system_info_btn)

        license_btn = QPushButton("📄 الترخيص")
        license_btn.clicked.connect(self.show_license)
        buttons_layout.addWidget(license_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        widget.setLayout(layout)
        return widget

    def show_faq_answer(self, item):
        """عرض إجابة السؤال المحدد"""
        answer = item.data(Qt.UserRole)
        if answer:
            self.faq_answer.setText(answer)

    def search_and_show_help(self):
        """البحث وعرض نافذة المساعدة"""
        query = self.search_input.text().strip()
        if query:
            results = self.help_content.search_content(query)
            if results:
                # عرض أول نتيجة في نافذة منفصلة
                dialog = HelpDialog(results[0]['id'], self)
                dialog.exec_()
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "البحث", "لم يتم العثور على نتائج مطابقة.")
        else:
            # عرض نافذة المساعدة العامة
            dialog = HelpDialog(parent=self)
            dialog.exec_()

    def show_help_dialog(self, section_id):
        """عرض نافذة المساعدة لقسم محدد"""
        dialog = HelpDialog(section_id, self)
        dialog.exec_()

    def check_for_updates(self):
        """فحص التحديثات"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "التحديثات",
            "أنت تستخدم أحدث إصدار من التطبيق.\n"
            "سيتم إشعارك تلقائياً عند توفر تحديثات جديدة.")

    def show_system_info(self):
        """عرض معلومات النظام"""
        import platform
        import sys
        from PyQt5.QtWidgets import QMessageBox

        info = f"""
        معلومات النظام:

        نظام التشغيل: {platform.system()} {platform.release()}
        المعمارية: {platform.machine()}
        المعالج: {platform.processor()}

        Python: {sys.version}
        PyQt5: {QMessageBox().metaObject().className()}

        مسار التطبيق: {os.getcwd()}
        """

        QMessageBox.information(self, "معلومات النظام", info)

    def show_license(self):
        """عرض معلومات الترخيص"""
        from PyQt5.QtWidgets import QMessageBox

        license_text = """
        رخصة الاستخدام

        هذا البرنامج مرخص للاستخدام الداخلي فقط.
        جميع الحقوق محفوظة.

        يُمنع:
        - إعادة التوزيع
        - التعديل على الكود المصدري
        - الاستخدام التجاري بدون ترخيص

        للحصول على ترخيص تجاري، يرجى التواصل معنا.
        """

        QMessageBox.information(self, "الترخيص", license_text)
