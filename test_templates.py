#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة تصدير القوالب
"""

import sys
import os
import sqlite3
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from ExportModule import ExportWidget

def create_test_database():
    """إنشاء قاعدة بيانات تجريبية"""
    conn = sqlite3.connect("test_activities.db")
    cursor = conn.cursor()
    
    # إنشاء جدول الأنشطة
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS activities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            person TEXT,
            activity_name TEXT,
            parent_category TEXT,
            sub_category TEXT,
            governorate TEXT,
            district TEXT,
            sub_district TEXT,
            village TEXT,
            participants INTEGER,
            target_participants INTEGER,
            budget REAL,
            actual_cost REAL,
            status TEXT,
            priority TEXT,
            activity_date TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # إنشاء جدول التصنيفات
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            parent_category TEXT,
            sub_category TEXT
        )
    """)
    
    # إدراج بيانات تجريبية
    test_activities = [
        ("أحمد محمد", "ورشة تدريبية", "التعليم", "التدريب المهني", "صنعاء", "الأمانة", "شعوب", "الحصبة", 50, 60, 10000, 9500, "مكتمل", "عالية", "2024-01-15"),
        ("فاطمة علي", "حملة توعية", "الصحة", "التوعية الصحية", "عدن", "المعلا", "كريتر", "التواهي", 100, 120, 5000, 4800, "جاري", "متوسطة", "2024-01-20"),
        ("محمد سالم", "مشروع تنموي", "التنمية", "التنمية المجتمعية", "تعز", "المدينة", "الوازعية", "الشمايتين", 200, 250, 25000, 0, "مخطط", "عالية", "2024-02-01"),
        ("سارة أحمد", "دورة تدريبية", "التعليم", "التعليم المستمر", "الحديدة", "الحديدة", "الحوك", "الحوك", 75, 80, 8000, 7500, "مكتمل", "متوسطة", "2024-01-25"),
        ("علي حسن", "مؤتمر علمي", "البحث العلمي", "المؤتمرات", "إب", "إب", "السبرة", "السبرة", 150, 200, 15000, 14000, "مكتمل", "عالية", "2024-02-10")
    ]
    
    cursor.executemany("""
        INSERT INTO activities (person, activity_name, parent_category, sub_category, 
                              governorate, district, sub_district, village, participants, 
                              target_participants, budget, actual_cost, status, priority, activity_date)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, test_activities)
    
    # إدراج تصنيفات تجريبية
    test_categories = [
        ("التعليم", "التدريب المهني"),
        ("التعليم", "التعليم المستمر"),
        ("الصحة", "التوعية الصحية"),
        ("التنمية", "التنمية المجتمعية"),
        ("البحث العلمي", "المؤتمرات")
    ]
    
    cursor.executemany("""
        INSERT INTO categories (parent_category, sub_category)
        VALUES (?, ?)
    """, test_categories)
    
    conn.commit()
    return conn

class TestTemplatesWindow(QMainWindow):
    """نافذة اختبار القوالب"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار ميزة تصدير القوالب")
        self.setGeometry(100, 100, 1000, 700)
        
        # إنشاء قاعدة البيانات التجريبية
        self.conn = create_test_database()
        
        # إعداد الواجهة
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # إضافة واجهة التصدير
        self.export_widget = ExportWidget(self.conn)
        layout.addWidget(self.export_widget)
        
    def closeEvent(self, event):
        """إغلاق قاعدة البيانات عند إغلاق النافذة"""
        if self.conn:
            self.conn.close()
        
        # حذف قاعدة البيانات التجريبية
        if os.path.exists("test_activities.db"):
            os.remove("test_activities.db")
            
        event.accept()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName("اختبار تصدير القوالب")
    
    # تطبيق نمط عربي
    app.setLayoutDirection(2)  # RTL
    
    window = TestTemplatesWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
