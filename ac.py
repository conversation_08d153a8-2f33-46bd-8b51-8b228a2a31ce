import sys
import sqlite3
import json
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QComboBox, QTextEdit, QSpinBox, QDateEdit, QTableWidget,
    QTableWidgetItem, QMessageBox, QDialog, QHeaderView, QFileDialog,
    QGroupBox, QTabWidget, QFormLayout, QCheckBox
)
from PyQt5.QtCore import QDate, Qt
from PyQt5.QtGui import QFont, QIcon
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import pandas as pd
from datetime import datetime
import mw  
from ModernDialog import ModernDialog
from ChartWidgets import AnimatedPieChart, AnimatedBarChart, AnimatedLineChart
from ReportsModule import AdvancedReportsWidget
from ModernUI import ModernCard, ModernButton, ModernProgressBar, ModernMainWindow, ModernPopupManager
from ExportModule import ExportWidget
from ImportModule import ImportWidget
from DashboardModule import DashboardWidget
from BackupModule import BackupRestoreWidget
from PerformanceModule import PerformanceWidget
from HelpModule import HelpWidget

# إنشاء قاعدة البيانات مع تحسينات
conn = sqlite3.connect("activities.db")
cursor = conn.cursor()

# إنشاء الجداول مع تحسينات
cursor.execute('''
CREATE TABLE IF NOT EXISTS activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    person TEXT NOT NULL,
    activity_name TEXT NOT NULL,
    parent_category TEXT,
    sub_category TEXT,
    is_approved TEXT CHECK(is_approved IN ('نعم', 'لا')) DEFAULT 'لا',
    governorate TEXT,
    district TEXT,
    sub_district TEXT,
    village TEXT,
    participants INTEGER DEFAULT 0,
    target_participants INTEGER DEFAULT 0,
    male_participants INTEGER DEFAULT 0,
    female_participants INTEGER DEFAULT 0,
    budget REAL DEFAULT 0.0,
    actual_cost REAL DEFAULT 0.0,
    results TEXT,
    recommendations TEXT,
    notes TEXT,
    activity_date TEXT NOT NULL,
    start_date TEXT,
    end_date TEXT,
    status TEXT CHECK(status IN ('مخطط', 'جاري', 'مكتمل', 'ملغي')) DEFAULT 'مخطط',
    priority TEXT CHECK(priority IN ('عالي', 'متوسط', 'منخفض')) DEFAULT 'متوسط',
    location_coordinates TEXT,
    contact_person TEXT,
    contact_phone TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
)
''')

# إنشاء جدول للفئات المحسن
cursor.execute('''
CREATE TABLE IF NOT EXISTS categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parent_category TEXT NOT NULL,
    sub_category TEXT NOT NULL,
    description TEXT,
    color_code TEXT DEFAULT '#4CAF50',
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(parent_category, sub_category)
)
''')

# إنشاء جدول للمحافظات والمناطق
cursor.execute('''
CREATE TABLE IF NOT EXISTS locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    governorate TEXT NOT NULL,
    district TEXT,
    sub_district TEXT,
    village TEXT,
    population INTEGER DEFAULT 0,
    coordinates TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
)
''')

# إنشاء جدول للأشخاص/المنفذين
cursor.execute('''
CREATE TABLE IF NOT EXISTS persons (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    position TEXT,
    organization TEXT,
    phone TEXT,
    email TEXT,
    specialization TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
)
''')

# إنشاء جدول للتقارير المحفوظة
cursor.execute('''
CREATE TABLE IF NOT EXISTS saved_reports (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    report_name TEXT NOT NULL,
    report_type TEXT NOT NULL,
    filters TEXT,
    created_by TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    last_accessed TEXT DEFAULT CURRENT_TIMESTAMP
)
''')

# إنشاء جدول لسجل النشاطات (للتدقيق)
cursor.execute('''
CREATE TABLE IF NOT EXISTS activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    activity_id INTEGER,
    action TEXT NOT NULL,
    old_values TEXT,
    new_values TEXT,
    user_name TEXT,
    timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (activity_id) REFERENCES activities (id)
)
''')

# إضافة بعض الفئات الافتراضية المحسنة
default_categories = [
    ('التعليم', 'محو الأمية', 'برامج محو الأمية للكبار', '#2196F3'),
    ('التعليم', 'تدريب المعلمين', 'تطوير قدرات المعلمين', '#2196F3'),
    ('التعليم', 'التعليم المهني', 'برامج التدريب المهني', '#2196F3'),
    ('الصحة', 'توعية صحية', 'برامج التوعية الصحية', '#4CAF50'),
    ('الصحة', 'حملات تطعيم', 'حملات التطعيم والوقاية', '#4CAF50'),
    ('الصحة', 'الصحة الإنجابية', 'برامج الصحة الإنجابية', '#4CAF50'),
    ('الزراعة', 'تدريب زراعي', 'تدريب المزارعين', '#8BC34A'),
    ('الزراعة', 'توزيع بذور', 'توزيع البذور والأسمدة', '#8BC34A'),
    ('الزراعة', 'الإرشاد الزراعي', 'خدمات الإرشاد الزراعي', '#8BC34A'),
    ('التنمية الاجتماعية', 'تمكين المرأة', 'برامج تمكين المرأة', '#E91E63'),
    ('التنمية الاجتماعية', 'رعاية الأطفال', 'برامج رعاية الطفولة', '#E91E63'),
    ('التنمية الاجتماعية', 'كبار السن', 'رعاية كبار السن', '#E91E63'),
    ('البنية التحتية', 'المياه والصرف', 'مشاريع المياه والصرف الصحي', '#FF9800'),
    ('البنية التحتية', 'الطرق', 'إنشاء وصيانة الطرق', '#FF9800'),
    ('البنية التحتية', 'الكهرباء', 'مشاريع الطاقة والكهرباء', '#FF9800'),
    ('البيئة', 'حماية البيئة', 'برامج حماية البيئة', '#009688'),
    ('البيئة', 'التشجير', 'حملات التشجير', '#009688'),
    ('البيئة', 'إدارة النفايات', 'برامج إدارة النفايات', '#009688')
]

cursor.executemany('''
INSERT OR IGNORE INTO categories (parent_category, sub_category, description, color_code) VALUES (?, ?, ?, ?)
''', default_categories)

# إضافة بعض المحافظات والمناطق الافتراضية
default_locations = [
    ('صنعاء', 'مديرية الصافية', 'عزلة بني حشيش', 'قرية الروضة'),
    ('صنعاء', 'مديرية شعوب', 'عزلة شعوب', 'قرية شعوب'),
    ('صنعاء', 'مديرية السبعين', 'عزلة السبعين', 'حي السبعين'),
    ('عدن', 'مديرية المعلا', 'عزلة المعلا', 'حي المعلا'),
    ('عدن', 'مديرية كريتر', 'عزلة كريتر', 'حي كريتر'),
    ('تعز', 'مديرية صالة', 'عزلة صالة', 'قرية صالة'),
    ('الحديدة', 'مديرية الحديدة', 'عزلة الحديدة', 'حي الحديدة'),
    ('إب', 'مديرية إب', 'عزلة إب', 'مدينة إب'),
    ('ذمار', 'مديرية ذمار', 'عزلة ذمار', 'مدينة ذمار'),
    ('حضرموت', 'مديرية المكلا', 'عزلة المكلا', 'مدينة المكلا')
]

cursor.executemany('''
INSERT OR IGNORE INTO locations (governorate, district, sub_district, village) VALUES (?, ?, ?, ?)
''', default_locations)

conn.commit()

class EnhancedEntryDialog(ModernDialog):
    def __init__(self, activity_id=None):
        super().__init__()
        self.activity_id = activity_id
        self.setWindowTitle("إضافة/تعديل نشاط")
        self.setWindowIcon(QIcon('activity.png'))
        self.setMinimumWidth(600)
        self.fields = {}
        
        # إنشاء تخطيط رئيسي مع تبويبات
        main_layout = QVBoxLayout()
        tab_widget = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_info_tab = QWidget()
        basic_layout = QFormLayout()
        
        # حقول النموذج مع تحسينات
        fields_info = [
            ("person", "الشخص", "QComboBox", True),
            ("activity_name", "اسم النشاط", "QLineEdit", True),
            ("parent_category", "التصنيف الأب", "QComboBox", True),
            ("sub_category", "التصنيف الابن", "QComboBox", True),
            ("is_approved", "معتمد", "QComboBox", True),
            ("status", "حالة النشاط", "QComboBox", False),
            ("priority", "الأولوية", "QComboBox", False),
            ("governorate", "المحافظة", "QComboBox", False),
            ("district", "المديرية", "QComboBox", False),
            ("sub_district", "العزلة", "QComboBox", False),
            ("village", "القرية", "QComboBox", False),
            ("participants", "عدد المشاركين الفعلي", "QSpinBox", False),
            ("target_participants", "عدد المشاركين المستهدف", "QSpinBox", False),
            ("male_participants", "عدد المشاركين الذكور", "QSpinBox", False),
            ("female_participants", "عدد المشاركين الإناث", "QSpinBox", False)
        ]
        
        # تعبئة حقول التصنيفات والبيانات المرجعية
        cursor.execute("SELECT DISTINCT parent_category FROM categories ORDER BY parent_category")
        parent_categories = [row[0] for row in cursor.fetchall()]

        cursor.execute("SELECT DISTINCT name FROM persons WHERE is_active = 1 ORDER BY name")
        persons = [row[0] for row in cursor.fetchall()]

        cursor.execute("SELECT DISTINCT governorate FROM locations ORDER BY governorate")
        governorates = [row[0] for row in cursor.fetchall()]

        for key, text, field_type, required in fields_info:
            if field_type == "QLineEdit":
                field = QLineEdit()
                if required:
                    field.setPlaceholderText("مطلوب")
            elif field_type == "QComboBox":
                field = QComboBox()
                field.setEditable(True)  # السماح بالتحرير

                if key == "person":
                    field.addItems([""] + persons)
                elif key == "parent_category":
                    field.addItems([""] + parent_categories)
                    field.currentTextChanged.connect(self.update_sub_categories)
                elif key == "sub_category":
                    self.update_sub_categories(parent_categories[0] if parent_categories else "")
                elif key == "is_approved":
                    field.addItems(["لا", "نعم"])
                elif key == "status":
                    field.addItems(["مخطط", "جاري", "مكتمل", "ملغي"])
                elif key == "priority":
                    field.addItems(["متوسط", "عالي", "منخفض"])
                elif key == "governorate":
                    field.addItems([""] + governorates)
                    field.currentTextChanged.connect(self.update_districts)
                elif key in ["district", "sub_district", "village"]:
                    # سيتم تعبئتها ديناميكياً
                    pass

            elif field_type == "QSpinBox":
                field = QSpinBox()
                field.setMinimum(0)
                field.setMaximum(99999)

            self.fields[key] = field
            label = QLabel(text)
            if required:
                label.setText(f"{text} *")
            basic_layout.addRow(label, field)
        
        # تبويب التفاصيل الإضافية
        details_tab = QWidget()
        details_layout = QFormLayout()

        # حقول الميزانية والتكلفة
        budget_fields = [
            ("budget", "الميزانية المخططة", "QDoubleSpinBox"),
            ("actual_cost", "التكلفة الفعلية", "QDoubleSpinBox"),
            ("contact_person", "الشخص المسؤول", "QLineEdit"),
            ("contact_phone", "رقم الهاتف", "QLineEdit"),
            ("location_coordinates", "الإحداثيات", "QLineEdit")
        ]

        for key, text, field_type in budget_fields:
            if field_type == "QDoubleSpinBox":
                field = QSpinBox()
                field.setMinimum(0)
                field.setMaximum(999999999)
                field.setSuffix(" ريال")
            elif field_type == "QLineEdit":
                field = QLineEdit()
                if key == "contact_phone":
                    field.setPlaceholderText("مثال: 777123456")
                elif key == "location_coordinates":
                    field.setPlaceholderText("مثال: 15.3694, 44.1910")

            self.fields[key] = field
            details_layout.addRow(QLabel(text), field)

        # إضافة فاصل
        details_layout.addRow(QLabel(""), QLabel(""))

        # حقول النصوص الطويلة
        text_fields = [
            ("results", "أبرز النتائج", "QTextEdit"),
            ("recommendations", "التوصيات", "QTextEdit"),
            ("notes", "ملاحظات", "QTextEdit")
        ]

        for key, text, field_type in text_fields:
            if field_type == "QTextEdit":
                field = QTextEdit()
                field.setMaximumHeight(80)
            self.fields[key] = field
            details_layout.addRow(QLabel(text), field)
        
        # تبويب التواريخ
        dates_tab = QWidget()
        dates_layout = QFormLayout()

        # تاريخ النشاط الرئيسي
        self.fields["activity_date"] = QDateEdit()
        self.fields["activity_date"].setDate(QDate.currentDate())
        self.fields["activity_date"].setCalendarPopup(True)
        dates_layout.addRow(QLabel("تاريخ النشاط *"), self.fields["activity_date"])

        # تاريخ البداية
        self.fields["start_date"] = QDateEdit()
        self.fields["start_date"].setDate(QDate.currentDate())
        self.fields["start_date"].setCalendarPopup(True)
        dates_layout.addRow(QLabel("تاريخ البداية"), self.fields["start_date"])

        # تاريخ النهاية
        self.fields["end_date"] = QDateEdit()
        self.fields["end_date"].setDate(QDate.currentDate())
        self.fields["end_date"].setCalendarPopup(True)
        dates_layout.addRow(QLabel("تاريخ النهاية"), self.fields["end_date"])

        dates_tab.setLayout(dates_layout)
        
        # إضافة التبويبات إلى ويدجت التبويبات
        basic_info_tab.setLayout(basic_layout)
        details_tab.setLayout(details_layout)
        dates_tab.setLayout(dates_layout)
        
        tab_widget.addTab(basic_info_tab, "معلومات أساسية")
        tab_widget.addTab(details_tab, "تفاصيل إضافية")
        tab_widget.addTab(dates_tab, "التواريخ")
        
        main_layout.addWidget(tab_widget)
        
        # أزرار الحفظ والإلغاء مع تحسينات
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setIcon(QIcon('save.png'))
        save_btn.clicked.connect(self.save_entry)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setIcon(QIcon('cancel.png'))
        cancel_btn.clicked.connect(self.reject)
        
        btn_layout.addWidget(save_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(btn_layout)
        self.setLayout(main_layout)
        
        # تحميل البيانات عند التعديل
        if activity_id:
            self.load_activity_data(activity_id)
    
    def update_sub_categories(self, parent_category):
        if "sub_category" in self.fields:
            self.fields["sub_category"].clear()
            if parent_category:
                cursor.execute("SELECT sub_category FROM categories WHERE parent_category=? ORDER BY sub_category", (parent_category,))
                sub_categories = [row[0] for row in cursor.fetchall()]
                self.fields["sub_category"].addItems([""] + sub_categories)

    def update_districts(self, governorate):
        if "district" in self.fields:
            self.fields["district"].clear()
            self.fields["sub_district"].clear()
            self.fields["village"].clear()

            if governorate:
                cursor.execute("SELECT DISTINCT district FROM locations WHERE governorate=? ORDER BY district", (governorate,))
                districts = [row[0] for row in cursor.fetchall() if row[0]]
                self.fields["district"].addItems([""] + districts)

                # ربط تحديث العزل بتغيير المديرية
                self.fields["district"].currentTextChanged.connect(self.update_sub_districts)

    def update_sub_districts(self, district):
        if "sub_district" in self.fields:
            self.fields["sub_district"].clear()
            self.fields["village"].clear()

            governorate = self.fields["governorate"].currentText()
            if governorate and district:
                cursor.execute("SELECT DISTINCT sub_district FROM locations WHERE governorate=? AND district=? ORDER BY sub_district", (governorate, district))
                sub_districts = [row[0] for row in cursor.fetchall() if row[0]]
                self.fields["sub_district"].addItems([""] + sub_districts)

                # ربط تحديث القرى بتغيير العزلة
                self.fields["sub_district"].currentTextChanged.connect(self.update_villages)

    def update_villages(self, sub_district):
        if "village" in self.fields:
            self.fields["village"].clear()

            governorate = self.fields["governorate"].currentText()
            district = self.fields["district"].currentText()
            if governorate and district and sub_district:
                cursor.execute("SELECT DISTINCT village FROM locations WHERE governorate=? AND district=? AND sub_district=? ORDER BY village", (governorate, district, sub_district))
                villages = [row[0] for row in cursor.fetchall() if row[0]]
                self.fields["village"].addItems([""] + villages)
    
    def load_activity_data(self, activity_id):
        cursor.execute("SELECT * FROM activities WHERE id=?", (activity_id,))
        record = cursor.fetchone()
        if record:
            # الحصول على أسماء الأعمدة
            cursor.execute("PRAGMA table_info(activities)")
            columns = [column[1] for column in cursor.fetchall()]

            # إنشاء قاموس للبيانات
            data = dict(zip(columns, record))

            # تعبئة الحقول
            for key, field in self.fields.items():
                value = data.get(key, "")

                if isinstance(field, QLineEdit):
                    field.setText(str(value) if value else "")
                elif isinstance(field, QComboBox):
                    # تحديث القوائم المنسدلة أولاً
                    if key == "sub_category" and data.get("parent_category"):
                        self.update_sub_categories(data.get("parent_category"))
                    elif key == "district" and data.get("governorate"):
                        self.update_districts(data.get("governorate"))
                    elif key == "sub_district" and data.get("district"):
                        self.update_sub_districts(data.get("district"))
                    elif key == "village" and data.get("sub_district"):
                        self.update_villages(data.get("sub_district"))

                    # تعيين القيمة
                    index = field.findText(str(value) if value else "")
                    if index >= 0:
                        field.setCurrentIndex(index)
                    else:
                        field.setCurrentText(str(value) if value else "")

                elif isinstance(field, QTextEdit):
                    field.setPlainText(str(value) if value else "")
                elif isinstance(field, QSpinBox):
                    field.setValue(int(value) if value else 0)
                elif isinstance(field, QDateEdit):
                    if value:
                        try:
                            field.setDate(QDate.fromString(str(value), "yyyy-MM-dd"))
                        except:
                            field.setDate(QDate.currentDate())
    
    def save_entry(self):
        try:
            # التحقق من الحقول المطلوبة
            required_fields = ["person", "activity_name", "activity_date"]
            for field_name in required_fields:
                field = self.fields[field_name]
                value = ""

                if isinstance(field, QLineEdit):
                    value = field.text().strip()
                elif isinstance(field, QComboBox):
                    value = field.currentText().strip()
                elif isinstance(field, QDateEdit):
                    value = field.date().toString("yyyy-MM-dd")

                if not value:
                    QMessageBox.warning(self, "تحذير", f"حقل '{field_name}' مطلوب")
                    return

            # جمع البيانات
            data = {}
            for key, field in self.fields.items():
                if isinstance(field, QLineEdit):
                    data[key] = field.text().strip()
                elif isinstance(field, QComboBox):
                    data[key] = field.currentText().strip()
                elif isinstance(field, QTextEdit):
                    data[key] = field.toPlainText().strip()
                elif isinstance(field, QSpinBox):
                    data[key] = field.value()
                elif isinstance(field, QDateEdit):
                    data[key] = field.date().toString("yyyy-MM-dd")

            # التحقق من صحة البيانات
            if data.get("participants", 0) > data.get("target_participants", 0) and data.get("target_participants", 0) > 0:
                reply = QMessageBox.question(self, "تحذير",
                    "عدد المشاركين الفعلي أكبر من المستهدف. هل تريد المتابعة؟",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    return

            if self.activity_id:
                # تحديث سجل موجود
                # إنشاء استعلام التحديث ديناميكياً
                fields = []
                values = []
                for key, value in data.items():
                    if key != 'id':
                        fields.append(f"{key}=?")
                        values.append(value)

                values.append(self.activity_id)
                query = f"UPDATE activities SET {', '.join(fields)}, updated_at=CURRENT_TIMESTAMP WHERE id=?"

                # تسجيل التغيير في سجل النشاطات (يمكن إضافة منطق مقارنة لاحقاً)
                # cursor.execute("SELECT * FROM activities WHERE id=?", (self.activity_id,))

            else:
                # إضافة سجل جديد
                fields = list(data.keys())
                placeholders = ', '.join(['?' for _ in fields])
                values = list(data.values())
                query = f"INSERT INTO activities ({', '.join(fields)}) VALUES ({placeholders})"

            cursor.execute(query, values)

            # الحصول على معرف النشاط
            if not self.activity_id:
                activity_id = cursor.lastrowid
            else:
                activity_id = self.activity_id

            # إضافة سجل في جدول النشاطات
            action = "تحديث" if self.activity_id else "إضافة"
            cursor.execute('''
                INSERT INTO activity_log (activity_id, action, new_values, user_name)
                VALUES (?, ?, ?, ?)
            ''', (activity_id, action, json.dumps(data, ensure_ascii=False), "المستخدم الحالي"))

            conn.commit()

            # إضافة البيانات المرجعية الجديدة
            self.add_reference_data(data)

            QMessageBox.information(self, "تم الحفظ", "تم حفظ النشاط بنجاح")
            self.accept()

        except sqlite3.Error as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الحفظ: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ غير متوقع: {str(e)}")

    def add_reference_data(self, data):
        """إضافة البيانات المرجعية الجديدة إلى الجداول المناسبة"""
        try:
            # إضافة التصنيفات الجديدة
            if data.get("parent_category") and data.get("sub_category"):
                cursor.execute('''
                    INSERT OR IGNORE INTO categories (parent_category, sub_category)
                    VALUES (?, ?)
                ''', (data["parent_category"], data["sub_category"]))

            # إضافة الشخص الجديد
            if data.get("person"):
                cursor.execute('''
                    INSERT OR IGNORE INTO persons (name) VALUES (?)
                ''', (data["person"],))

            # إضافة الموقع الجديد
            if data.get("governorate"):
                cursor.execute('''
                    INSERT OR IGNORE INTO locations (governorate, district, sub_district, village)
                    VALUES (?, ?, ?, ?)
                ''', (data.get("governorate", ""), data.get("district", ""),
                     data.get("sub_district", ""), data.get("village", "")))

            conn.commit()

        except sqlite3.Error as e:
            print(f"خطأ في إضافة البيانات المرجعية: {e}")

class EnhancedActivityTracker(mw.ModernWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام تتبع الأنشطة المحسّن - الإصدار العصري")
        self.setWindowIcon(QIcon('tracker.png'))
        self.resize(1400, 900)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #ffffff);
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLabel {
                color: #2c3e50;
            }
        """)

        # تخطيط رئيسي مع نظام النوافذ المنبثقة العصري
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء مدير النوافذ المنبثقة
        self.popup_manager = ModernPopupManager()

        # إعداد النوافذ المنبثقة
        self.setup_popup_windows()

        main_layout.addWidget(self.popup_manager)
        self.setLayout(main_layout)

    def setup_popup_windows(self):
        """إعداد جميع النوافذ المنبثقة"""
        # نافذة لوحة المعلومات
        dashboard_widget = DashboardWidget(conn)
        self.popup_manager.add_popup_option(
            dashboard_widget,
            "لوحة المعلومات",
            "📊",
            "عرض الإحصائيات والمخططات الرئيسية"
        )

        # نافذة عرض البيانات
        data_widget = QWidget()
        self.setup_data_screen_for_popup(data_widget)
        self.popup_manager.add_popup_option(
            data_widget,
            "عرض البيانات",
            "📋",
            "جدول الأنشطة مع أدوات البحث والتصفية"
        )

        # نافذة الإحصائيات
        stats_widget = QWidget()
        self.setup_stats_screen_for_popup(stats_widget)
        self.popup_manager.add_popup_option(
            stats_widget,
            "الإحصائيات",
            "📈",
            "المخططات البيانية والتحليلات المتقدمة"
        )

        # نافذة التقارير المتقدمة
        reports_widget = AdvancedReportsWidget(conn)
        self.popup_manager.add_popup_option(
            reports_widget,
            "التقارير المتقدمة",
            "📑",
            "تقارير مفصلة وتحليلات شاملة"
        )

        # نافذة التصدير المتقدم
        export_widget = ExportWidget(conn)
        self.popup_manager.add_popup_option(
            export_widget,
            "التصدير المتقدم",
            "📤",
            "تصدير البيانات بصيغ متعددة"
        )

        # نافذة الاستيراد الذكي
        import_widget = ImportWidget(conn)
        self.popup_manager.add_popup_option(
            import_widget,
            "الاستيراد الذكي",
            "📥",
            "استيراد البيانات من ملفات خارجية"
        )

        # نافذة النسخ الاحتياطي والاستعادة
        backup_widget = BackupRestoreWidget(conn)
        self.popup_manager.add_popup_option(
            backup_widget,
            "النسخ الاحتياطي",
            "💾",
            "إدارة النسخ الاحتياطية والاستعادة"
        )

        # نافذة مراقبة الأداء
        performance_widget = PerformanceWidget(conn)
        self.popup_manager.add_popup_option(
            performance_widget,
            "مراقبة الأداء",
            "⚡",
            "مراقبة أداء التطبيق والنظام"
        )

        # نافذة المساعدة والتوثيق
        help_widget = HelpWidget()
        self.popup_manager.add_popup_option(
            help_widget,
            "المساعدة",
            "❓",
            "دليل المستخدم والمساعدة الفنية"
        )
        
        # تحميل البيانات الأولية
        self.load_data()
        self.update_charts()
    
    def setup_data_screen_for_popup(self, widget):
        layout = QVBoxLayout()
        
        # مجموعة تصفية البيانات
        filter_group = QGroupBox("تصفية البيانات")
        filter_layout = QHBoxLayout()
        
        # عناصر التصفية
        self.filter_field = QLineEdit()
        self.filter_field.setPlaceholderText("ابحث بأي حقل...")
        self.filter_field.textChanged.connect(self.load_data)
        
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع التصنيفات", "")
        cursor.execute("SELECT DISTINCT parent_category FROM categories")
        for row in cursor.fetchall():
            self.category_filter.addItem(row[0], row[0])
        self.category_filter.currentIndexChanged.connect(self.load_data)
        
        self.date_from_filter = QDateEdit()
        self.date_from_filter.setDate(QDate.currentDate().addMonths(-1))
        self.date_from_filter.setCalendarPopup(True)
        self.date_from_filter.dateChanged.connect(self.load_data)
        
        self.date_to_filter = QDateEdit()
        self.date_to_filter.setDate(QDate.currentDate())
        self.date_to_filter.setCalendarPopup(True)
        self.date_to_filter.dateChanged.connect(self.load_data)
        
        filter_layout.addWidget(QLabel("بحث:"))
        filter_layout.addWidget(self.filter_field)
        filter_layout.addWidget(QLabel("التصنيف:"))
        filter_layout.addWidget(self.category_filter)
        filter_layout.addWidget(QLabel("من:"))
        filter_layout.addWidget(self.date_from_filter)
        filter_layout.addWidget(QLabel("إلى:"))
        filter_layout.addWidget(self.date_to_filter)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # الجدول مع تحسينات
        self.table = QTableWidget()
        self.table.setColumnCount(15)
        self.table.setHorizontalHeaderLabels([
            "ID", "الشخص", "النشاط", "التصنيف الأب", "التصنيف الابن", "معتمد",
            "المحافظة", "المديرية", "العزلة", "القرية", "المشاركون",
            "النتائج", "التوصيات", "ملاحظات", "تاريخ النشاط"
        ])
        
        # تحسين مظهر الجدول
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Interactive)
        header.setStretchLastSection(True)
        self.table.verticalHeader().setDefaultSectionSize(30)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setSortingEnabled(True)
        
        layout.addWidget(self.table)
        
        # أزرار التحكم مع تحسينات
        btn_layout = QHBoxLayout()
        
        self.add_btn = QPushButton("إضافة نشاط")
        self.add_btn.setIcon(QIcon('add.png'))
        self.add_btn.clicked.connect(self.open_entry_dialog)
        
        self.edit_btn = QPushButton("تعديل")
        self.edit_btn.setIcon(QIcon('edit.png'))
        self.edit_btn.clicked.connect(self.edit_selected)
        
        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setIcon(QIcon('delete.png'))
        self.delete_btn.clicked.connect(self.delete_selected)
        
        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setIcon(QIcon('refresh.png'))
        self.refresh_btn.clicked.connect(self.load_data)
        
        self.export_excel_btn = QPushButton("تصدير Excel")
        self.export_excel_btn.setIcon(QIcon('excel.png'))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        
        self.export_csv_btn = QPushButton("تصدير CSV")
        self.export_csv_btn.setIcon(QIcon('csv.png'))
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        
        self.import_btn = QPushButton("استيراد")
        self.import_btn.setIcon(QIcon('import.png'))
        self.import_btn.clicked.connect(self.import_data)
        
        btn_layout.addWidget(self.add_btn)
        btn_layout.addWidget(self.edit_btn)
        btn_layout.addWidget(self.delete_btn)
        btn_layout.addWidget(self.refresh_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(self.export_excel_btn)
        btn_layout.addWidget(self.export_csv_btn)
        btn_layout.addWidget(self.import_btn)
        
        layout.addLayout(btn_layout)
        widget.setLayout(layout)
    
    def setup_stats_screen_for_popup(self, widget):
        layout = QVBoxLayout()
        
        # مخطط عدد الأنشطة حسب المحافظة
        self.figure1, self.ax1 = plt.subplots()
        self.canvas1 = FigureCanvas(self.figure1)
        
        # مخطط عدد الأنشطة حسب التاريخ
        self.figure2, self.ax2 = plt.subplots()
        self.canvas2 = FigureCanvas(self.figure2)
        
        # مخطط التوزيع حسب التصنيف
        self.figure3, self.ax3 = plt.subplots()
        self.canvas3 = FigureCanvas(self.figure3)
        
        # إضافة المخططات إلى التخطيط
        stats_layout = QHBoxLayout()
        stats_layout.addWidget(self.canvas1)
        stats_layout.addWidget(self.canvas2)
        
        layout.addLayout(stats_layout)
        layout.addWidget(self.canvas3)

        # إنشاء الرسم البياني الدائري للمخزون
        self.pie_chart = AnimatedPieChart(self, rtl=True)
        self.pie_chart.set_title("توزيع  حسب نوع الانشطة")
        self.pie_chart.setMinimumHeight(300)  # زيادة الارتفاع الأدنى
        layout.addWidget(self.pie_chart)

        # أزرار تحديث المخططات
        refresh_btn = QPushButton("تحديث المخططات")
        refresh_btn.clicked.connect(self.update_charts)
        layout.addWidget(refresh_btn)
        
        widget.setLayout(layout)

    
    def update_inventory_pie_chart(self):
        """تحديث الرسم البياني الدائري لتوزيع الأنشطة حسب التصنيف"""
        try:
            # جلب توزيع الأنشطة حسب التصنيف الأب
            cursor.execute('''
                SELECT parent_category, COUNT(*) 
                FROM activities 
                GROUP BY parent_category 
                ORDER BY COUNT(*) DESC
            ''')
            cat_data = cursor.fetchall()

            # تجهيز البيانات للرسم البياني الدائري
            chart_data = []
            for parent_category, count in cat_data:
                if count and parent_category:
                    chart_data.append((parent_category, count))

            # تعيين البيانات في الرسم البياني الدائري
            if chart_data:
                self.pie_chart.set_data(chart_data)
        except Exception as e:
            print(f"خطأ في تحديث الرسم البياني الدائري للأنشطة: {e}")

    
    def load_data(self):
        try:
            search_term = self.filter_field.text()
            category_filter = self.category_filter.currentData()
            date_from = self.date_from_filter.date().toString("yyyy-MM-dd")
            date_to = self.date_to_filter.date().toString("yyyy-MM-dd")
            
            query = '''SELECT 
                id, person, activity_name, parent_category, sub_category, is_approved,
                governorate, district, sub_district, village, participants,
                results, recommendations, notes, activity_date
                FROM activities 
                WHERE (person LIKE ? OR activity_name LIKE ? OR governorate LIKE ?)
                AND (parent_category = ? OR ? = '')
                AND (activity_date >= ?)
                AND (activity_date <= ?)
                ORDER BY activity_date DESC'''
            
            params = (
                f"%{search_term}%", f"%{search_term}%", f"%{search_term}%",
                category_filter, category_filter, date_from, date_to
            )
            
            cursor.execute(query, params)
            data = cursor.fetchall()
            
            self.table.setRowCount(len(data))
            for row_idx, row in enumerate(data):
                for col_idx, value in enumerate(row):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    
                    # تمييز التواريخ
                    if col_idx in [14, 15] and value:
                        item.setData(Qt.DisplayRole, value)
                        item.setData(Qt.UserRole, datetime.strptime(value, "%Y-%m-%d"))
                    
                    self.table.setItem(row_idx, col_idx, item)
            
            # ضبط أبعاد الأعمدة
            for i in range(self.table.columnCount()):
                if i in [0, 10]:  # أعمدة ID وعدد المشاركين
                    self.table.setColumnWidth(i, 50)
                elif i in [1, 2, 3, 4, 6]:  # أعمدة النصوص المهمة
                    self.table.setColumnWidth(i, 150)
                else:
                    self.table.setColumnWidth(i, 100)
        
        except sqlite3.Error as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")
    
    def open_entry_dialog(self):
        dialog = EnhancedEntryDialog()
        if dialog.exec_():
            self.load_data()
            self.update_charts()
    
    def edit_selected(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سطر للتعديل")
            return
        
        activity_id = int(selected[0].text())
        dialog = EnhancedEntryDialog(activity_id)
        if dialog.exec_():
            self.load_data()
            self.update_charts()
    
    def delete_selected(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سطر للحذف")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذا السجل؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            activity_id = int(selected[0].text())
            try:
                cursor.execute("DELETE FROM activities WHERE id=?", (activity_id,))
                conn.commit()
                self.load_data()
                self.update_charts()
                QMessageBox.information(self, "تم الحذف", "تم حذف النشاط بنجاح")
            except sqlite3.Error as e:
                QMessageBox.critical(self, "خطأ", f"فشل في الحذف: {str(e)}")
    
    def export_to_excel(self):
        try:
            df = pd.read_sql("SELECT * FROM activities", conn)
            
            # استبعاد الأعمدة الداخلية
            df = df.drop(columns=['created_at', 'updated_at'])
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "", 
                "Excel Files (*.xlsx);;All Files (*)"
            )
            
            if filename:
                if not filename.endswith('.xlsx'):
                    filename += '.xlsx'
                
                df.to_excel(filename, index=False, engine='openpyxl')
                QMessageBox.information(self, "نجاح", "تم تصدير البيانات إلى Excel بنجاح")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التصدير: {str(e)}")
    
    def export_to_csv(self):
        try:
            df = pd.read_sql("SELECT * FROM activities", conn)
            
            # استبعاد الأعمدة الداخلية
            df = df.drop(columns=['created_at', 'updated_at'])
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف CSV", "", 
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if filename:
                if not filename.endswith('.csv'):
                    filename += '.csv'
                
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                QMessageBox.information(self, "نجاح", "تم تصدير البيانات إلى CSV بنجاح")
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التصدير: {str(e)}")
    
    def import_data(self):
        try:
            options = QFileDialog.Options()
            filename, _ = QFileDialog.getOpenFileName(
                self, "استيراد بيانات", "",
                "Excel Files (*.xlsx);;CSV Files (*.csv);;JSON Files (*.json);;All Files (*)",
                options=options
            )
            
            if not filename:
                return
            
            if filename.endswith('.xlsx'):
                df = pd.read_excel(filename)
            elif filename.endswith('.csv'):
                df = pd.read_csv(filename)
            elif filename.endswith('.json'):
                df = pd.read_json(filename)
            else:
                QMessageBox.warning(self, "تحذير", "نوع الملف غير مدعوم")
                return
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['person', 'activity_name', 'activity_date']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                QMessageBox.warning(
                    self, "تحذير", 
                    f"الملف لا يحتوي على الأعمدة المطلوبة: {', '.join(missing_columns)}"
                )
                return
            
            # استيراد البيانات
            imported = 0
            for _, row in df.iterrows():
                try:
                    # معالجة القيم المفقودة
                    data = {
                        'person': str(row.get('person', '')),
                        'activity_name': str(row.get('activity_name', '')),
                        'parent_category': str(row.get('parent_category', '')),
                        'sub_category': str(row.get('sub_category', '')),
                        'is_approved': 'نعم' if row.get('is_approved', '') in ['نعم', 'Yes', 'True', True] else 'لا',
                        'governorate': str(row.get('governorate', '')),
                        'district': str(row.get('district', '')),
                        'sub_district': str(row.get('sub_district', '')),
                        'village': str(row.get('village', '')),
                        'participants': int(row.get('participants', 0)),
                        'results': str(row.get('results', '')),
                        'recommendations': str(row.get('recommendations', '')),
                        'notes': str(row.get('notes', '')),
                        'activity_date': pd.to_datetime(row['activity_date']).strftime('%Y-%m-%d')
                    }
                    
                    # إدراج البيانات
                    cursor.execute('''
                    INSERT INTO activities (
                        person, activity_name, parent_category, sub_category,
                        is_approved, governorate, district, sub_district, village,
                        participants, results, recommendations, notes, activity_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', tuple(data.values()))
                    
                    imported += 1
                except Exception as e:
                    print(f"Error importing row: {e}")
            
            conn.commit()
            QMessageBox.information(
                self, "تم الاستيراد", 
                f"تم استيراد {imported} من أصل {len(df)} سجلاً بنجاح"
            )
            
            self.load_data()
            self.update_charts()
        
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الاستيراد: {str(e)}")
    
    def update_charts(self):
        try:
            # مخطط الأنشطة حسب المحافظة
            cursor.execute("SELECT governorate, COUNT(*) FROM activities GROUP BY governorate ORDER BY COUNT(*) DESC")
            gov_data = cursor.fetchall()
            
            self.ax1.clear()
            if gov_data:
                names = [row[0] or 'غير محدد' for row in gov_data]
                counts = [row[1] for row in gov_data]
                self.ax1.bar(names, counts, color='#4e79a7')
                self.ax1.set_title("عدد الأنشطة حسب المحافظة", fontsize=10)
                self.ax1.set_xlabel("المحافظة", fontsize=8)
                self.ax1.set_ylabel("عدد الأنشطة", fontsize=8)
                self.ax1.tick_params(axis='x', rotation=45, labelsize=8)
                self.ax1.tick_params(axis='y', labelsize=8)
                self.figure1.tight_layout()
            
            # مخطط الأنشطة حسب التاريخ
            cursor.execute('''
            SELECT strftime('%Y-%m', activity_date) as month, COUNT(*) 
            FROM activities 
            GROUP BY strftime('%Y-%m', activity_date) 
            ORDER BY month
            ''')
            date_data = cursor.fetchall()
            
            self.ax2.clear()
            if date_data:
                dates = [row[0] for row in date_data]
                counts = [row[1] for row in date_data]
                self.ax2.plot(dates, counts, marker='o', color='#e15759')
                self.ax2.set_title("عدد الأنشطة حسب الشهر", fontsize=10)
                self.ax2.set_xlabel("الشهر", fontsize=8)
                self.ax2.set_ylabel("عدد الأنشطة", fontsize=8)
                self.ax2.tick_params(axis='x', rotation=45, labelsize=8)
                self.ax2.tick_params(axis='y', labelsize=8)
                self.figure2.tight_layout()
            
            # مخطط التوزيع حسب التصنيف
            cursor.execute('''
            SELECT parent_category, COUNT(*) 
            FROM activities 
            GROUP BY parent_category 
            ORDER BY COUNT(*) DESC
            ''')
            cat_data = cursor.fetchall()
            
            self.ax3.clear()
            if cat_data:
                labels = [row[0] or 'غير محدد' for row in cat_data]
                sizes = [row[1] for row in cat_data]
                colors = plt.cm.Pastel1(range(len(labels)))
                self.ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                self.ax3.set_title("توزيع الأنشطة حسب التصنيف", fontsize=10)
                self.ax3.axis('equal')
                self.figure3.tight_layout()
            
            # رسم المخططات
            self.canvas1.draw()
            self.canvas2.draw()
            self.canvas3.draw()
        
        except sqlite3.Error as e:
            print(f"Error updating charts: {e}")
    
    def closeEvent(self, event):
        conn.close()
        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # تحسين مظهر التطبيق
    app.setStyle('Fusion')
    app.setLayoutDirection(Qt.RightToLeft)
    font = QFont()
    font.setFamily('Arial')
    font.setPointSize(10)
    app.setFont(font)
    
    window = EnhancedActivityTracker()
    window.show()
    sys.exit(app.exec_())