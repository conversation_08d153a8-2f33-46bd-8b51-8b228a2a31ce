# دليل نظام النوافذ المنبثقة الجديد

## نظرة عامة

تم تحويل التطبيق من نظام التبويبات (Tabs) إلى نظام النوافذ المنبثقة (Popup Windows) حيث تفتح كل شاشة في نافذة منفصلة تماماً عن النافذة الرئيسية.

## التغييرات الرئيسية

### 1. إضافة فئات النوافذ المنبثقة

تم إضافة فئات جديدة في `ModernUI.py`:

#### `ModernPopupWindow`
- نافذة منبثقة عصرية مستقلة
- تصميم متقدم مع شريط قوائم وحالة
- إمكانية تخصيص العنوان والأيقونة

#### `ModernPopupManager`
- مدير النوافذ المنبثقة الرئيسي
- واجهة رئيسية تحتوي على أزرار لفتح النوافذ
- نظام إدارة النوافذ المفتوحة
- تصميم شبكي للأزرار مع أوصاف

### 2. تحديث `EnhancedActivityTracker`

تم تحديث الفئة الرئيسية في `ac.py`:
- استبدال `ModernScreenManager` بـ `ModernPopupManager`
- تحويل `setup_data_screen()` إلى `setup_data_screen_for_popup()`
- تحويل `setup_stats_screen()` إلى `setup_stats_screen_for_popup()`
- إضافة دالة `setup_popup_windows()` لإعداد جميع النوافذ المنبثقة

## الشاشات المتاحة

1. **📊 لوحة المعلومات** - عرض الإحصائيات والمخططات الرئيسية
2. **📋 عرض البيانات** - جدول الأنشطة مع أدوات البحث والتصفية
3. **📈 الإحصائيات** - المخططات البيانية والتحليلات
4. **📑 التقارير المتقدمة** - تقارير مفصلة وتحليلات متقدمة
5. **📤 التصدير المتقدم** - أدوات تصدير البيانات
6. **📥 الاستيراد الذكي** - أدوات استيراد البيانات
7. **💾 النسخ الاحتياطي** - إدارة النسخ الاحتياطية
8. **⚡ مراقبة الأداء** - مراقبة أداء التطبيق
9. **❓ المساعدة** - دليل المستخدم والمساعدة

## مميزات نظام النوافذ المنبثقة الجديد

### التصميم
- نوافذ منبثقة مستقلة تماماً عن النافذة الرئيسية
- أزرار عصرية في شبكة منظمة مع أوصاف
- تأثيرات بصرية متقدمة (gradients, hover effects, shadows)
- تصميم متجاوب ومتوافق مع الشاشات المختلفة
- دعم كامل للغة العربية (RTL)

### الوظائف
- فتح نوافذ منفصلة لكل قسم
- إمكانية فتح عدة نوافذ في نفس الوقت
- إدارة ذكية للنوافذ المفتوحة (منع التكرار)
- إحضار النافذة للمقدمة إذا كانت مفتوحة مسبقاً
- إمكانية إضافة نوافذ جديدة بسهولة
- تخصيص أيقونات وأوصاف الأزرار

### الأداء
- تحميل النوافذ عند الحاجة فقط
- استقلالية كل نافذة عن الأخرى
- إمكانية إغلاق النوافذ بشكل منفصل
- استهلاك ذاكرة محسن

## كيفية الاستخدام

### للمستخدم النهائي
1. شغل التطبيق باستخدام `python ac.py`
2. ستظهر النافذة الرئيسية مع شبكة من الأزرار
3. انقر على أي زر لفتح النافذة المطلوبة في نافذة منفصلة
4. يمكن فتح عدة نوافذ في نفس الوقت
5. إذا نقرت على زر لنافذة مفتوحة مسبقاً، ستظهر في المقدمة
6. أغلق النوافذ بشكل منفصل حسب الحاجة

### للمطورين
```python
# إنشاء مدير نوافذ منبثقة جديد
popup_manager = ModernPopupManager()

# إضافة نافذة منبثقة جديدة
my_widget = QWidget()
popup_manager.add_popup_option(
    my_widget,
    "اسم النافذة",
    "🔧",
    "وصف النافذة"
)

# فتح نافذة محددة برمجياً
popup_manager.open_popup_window("اسم النافذة", "🔧")

# إغلاق نافذة محددة
popup_manager.close_popup_window("اسم النافذة")

# إغلاق جميع النوافذ
popup_manager.close_all_popups()
```

## ملف الاختبار

تم تحديث ملف `test_screen_system.py` لاختبار نظام النوافذ المنبثقة:
```bash
python test_screen_system.py
```

هذا الملف يحتوي على:
- نافذة رئيسية مع أزرار لفتح النوافذ المنبثقة
- ثلاث نوافذ اختبار مختلفة
- أمثلة على التصميم والوظائف

## التخصيص

### إضافة نافذة منبثقة جديدة
1. أنشئ فئة جديدة ترث من `QWidget`
2. أضف النافذة باستخدام `popup_manager.add_popup_option()`
3. حدد الاسم والأيقونة والوصف المناسب

### تخصيص التصميم
- عدل الـ CSS في `ModernPopupManager.setup_style()`
- عدل تصميم النوافذ في `ModernPopupWindow.setup_style()`
- غير الألوان والخطوط حسب الحاجة
- أضف تأثيرات بصرية جديدة

### إدارة النوافذ المتقدمة
- استخدم `popup_windows` dictionary لتتبع النوافذ المفتوحة
- استخدم `window_contents` dictionary لحفظ محتويات النوافذ
- يمكن إضافة وظائف إضافية مثل ترتيب النوافذ أو حفظ المواقع

## المتطلبات

- Python 3.6+
- PyQt5
- جميع المكتبات الموجودة في `requirements.txt`

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع شاشة المساعدة في التطبيق
2. تحقق من ملف `application.log`
3. استخدم ملف الاختبار للتأكد من عمل النظام

---

## المزايا الجديدة

### تعدد النوافذ
- يمكن فتح عدة أقسام في نفس الوقت
- كل نافذة مستقلة تماماً عن الأخرى
- إمكانية مقارنة البيانات بين النوافذ المختلفة

### سهولة الاستخدام
- واجهة رئيسية بسيطة ومنظمة
- أوصاف واضحة لكل قسم
- تصميم بديهي وسهل الفهم

### المرونة
- إمكانية تخصيص حجم ومكان كل نافذة
- حفظ حالة كل نافذة بشكل منفصل
- إدارة ذكية للذاكرة والموارد

---

**ملاحظة**: هذا التحديث يحول التطبيق إلى نظام نوافذ متعددة حديث مع الحفاظ على جميع الوظائف الموجودة وتحسين تجربة المستخدم بشكل كبير.
