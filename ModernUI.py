"""
وحدة التصميم العصري للواجهة
تتضمن تحسينات بصرية وتفاعلية متقدمة
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QGraphicsDropShadowEffect, QScrollArea, QGridLayout,
    QSizePolicy, QSpacerItem, QProgressBar, QGroupBox, QTabWidget,
    QApplication, QMainWindow, QStatusBar, QMenuBar, QAction,
    QToolBar, QSplitter, QDockWidget, QTextEdit, QListWidget,
    QListWidgetItem, QStackedWidget
)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, QTimer, pyqtSignal
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QLinearGradient, QPainter, QBrush,
    QPixmap, QIcon, QPen, QFontMetrics
)
import sys

class ModernCard(QFrame):
    """بطاقة عصرية مع تأثيرات بصرية"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title="", content="", icon_path="", color="#4CAF50"):
        super().__init__()
        self.title = title
        self.content = content
        self.icon_path = icon_path
        self.color = color
        self.setup_ui()
        self.setup_effects()
        
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 15px;
            }}
            QFrame:hover {{
                border: 2px solid {self.color};
                background-color: #fafafa;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(10)
        
        # العنوان
        if self.title:
            title_label = QLabel(self.title)
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            title_label.setStyleSheet(f"color: {self.color}; margin-bottom: 5px;")
            layout.addWidget(title_label)
        
        # المحتوى
        if self.content:
            content_label = QLabel(self.content)
            content_label.setFont(QFont("Arial", 11))
            content_label.setStyleSheet("color: #666; line-height: 1.4;")
            content_label.setWordWrap(True)
            layout.addWidget(content_label)
        
        self.setLayout(layout)
        
    def setup_effects(self):
        """إعداد التأثيرات البصرية"""
        # تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        # تحديد الحد الأدنى للحجم
        self.setMinimumHeight(120)
        self.setMinimumWidth(200)
        
    def mousePressEvent(self, event):
        """معالجة النقر على البطاقة"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class ModernButton(QPushButton):
    """زر عصري مع تأثيرات متقدمة"""
    
    def __init__(self, text="", icon_path="", button_type="primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setup_style()
        
        if icon_path:
            self.setIcon(QIcon(icon_path))
            
    def setup_style(self):
        """إعداد تصميم الزر"""
        if self.button_type == "primary":
            style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #4CAF50, stop:1 #45a049);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #5CBF60, stop:1 #4CAF50);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #45a049, stop:1 #3d8b40);
                }
            """
        elif self.button_type == "secondary":
            style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2196F3, stop:1 #1976D2);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #42A5F5, stop:1 #2196F3);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1976D2, stop:1 #1565C0);
                }
            """
        elif self.button_type == "danger":
            style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f44336, stop:1 #d32f2f);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ef5350, stop:1 #f44336);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d32f2f, stop:1 #c62828);
                }
            """
        else:  # outline
            style = """
                QPushButton {
                    background: transparent;
                    color: #4CAF50;
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    padding: 10px 22px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background: #4CAF50;
                    color: white;
                }
                QPushButton:pressed {
                    background: #45a049;
                    border-color: #45a049;
                }
            """
        
        self.setStyleSheet(style)


class ModernProgressBar(QProgressBar):
    """شريط تقدم عصري"""
    
    def __init__(self, color="#4CAF50"):
        super().__init__()
        self.color = color
        self.setup_style()
        
    def setup_style(self):
        """إعداد تصميم شريط التقدم"""
        style = f"""
            QProgressBar {{
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                text-align: center;
                background-color: #f5f5f5;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.color}, stop:1 #66BB6A);
                border-radius: 6px;
                margin: 2px;
            }}
        """
        self.setStyleSheet(style)


class ModernTabWidget(QTabWidget):
    """تبويبات عصرية"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
        
    def setup_style(self):
        """إعداد تصميم التبويبات"""
        style = """
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 100px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border-bottom: none;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e8, stop:1 #d4edda);
                color: #2e7d32;
            }
        """
        self.setStyleSheet(style)


class ModernSidebar(QFrame):
    """شريط جانبي عصري"""
    
    item_clicked = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """إعداد واجهة الشريط الجانبي"""
        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(10, 20, 10, 20)
        
        # عنوان الشريط الجانبي
        title = QLabel("القائمة الرئيسية")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # عناصر القائمة
        menu_items = [
            ("الأنشطة", "📋"),
            ("الإحصائيات", "📊"),
            ("التقارير", "📈"),
            ("الإعدادات", "⚙️"),
            ("المساعدة", "❓")
        ]
        
        for item_text, icon in menu_items:
            item_widget = self.create_menu_item(item_text, icon)
            layout.addWidget(item_widget)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def create_menu_item(self, text, icon):
        """إنشاء عنصر قائمة"""
        item = QPushButton(f"{icon} {text}")
        item.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 15px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                color: #34495e;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e8f5e8, stop:1 #d4edda);
                color: #2e7d32;
            }
            QPushButton:pressed {
                background: #4CAF50;
                color: white;
            }
        """)
        item.clicked.connect(lambda: self.item_clicked.emit(text))
        return item
        
    def setup_style(self):
        """إعداد تصميم الشريط الجانبي"""
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-right: 1px solid #dee2e6;
            }
        """)
        self.setFixedWidth(250)


class ModernStatusBar(QStatusBar):
    """شريط حالة عصري"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
        self.setup_widgets()
        
    def setup_style(self):
        """إعداد تصميم شريط الحالة"""
        self.setStyleSheet("""
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-top: 1px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
            QStatusBar::item {
                border: none;
            }
        """)
        
    def setup_widgets(self):
        """إعداد عناصر شريط الحالة"""
        # رسالة الحالة الرئيسية
        self.showMessage("جاهز")
        
        # مؤشر الاتصال
        self.connection_label = QLabel("🟢 متصل")
        self.connection_label.setStyleSheet("color: #28a745; font-weight: bold;")
        self.addPermanentWidget(self.connection_label)
        
        # عداد السجلات
        self.records_label = QLabel("السجلات: 0")
        self.records_label.setStyleSheet("color: #6c757d; margin-left: 20px;")
        self.addPermanentWidget(self.records_label)
        
    def update_records_count(self, count):
        """تحديث عداد السجلات"""
        self.records_label.setText(f"السجلات: {count}")
        
    def update_connection_status(self, connected):
        """تحديث حالة الاتصال"""
        if connected:
            self.connection_label.setText("🟢 متصل")
            self.connection_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.connection_label.setText("🔴 غير متصل")
            self.connection_label.setStyleSheet("color: #dc3545; font-weight: bold;")


class ModernMainWindow(QMainWindow):
    """النافذة الرئيسية العصرية"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """إعداد واجهة النافذة الرئيسية"""
        self.setWindowTitle("نظام إدارة الأنشطة - الإصدار المحسن")
        self.setGeometry(100, 100, 1400, 900)
        
        # الشريط الجانبي
        self.sidebar = ModernSidebar()
        sidebar_dock = QDockWidget("القائمة", self)
        sidebar_dock.setWidget(self.sidebar)
        sidebar_dock.setFeatures(QDockWidget.NoDockWidgetFeatures)
        self.addDockWidget(Qt.LeftDockWidgetArea, sidebar_dock)
        
        # المنطقة الرئيسية
        self.central_widget = QStackedWidget()
        self.setCentralWidget(self.central_widget)
        
        # شريط الحالة
        self.status_bar = ModernStatusBar()
        self.setStatusBar(self.status_bar)
        
        # ربط الأحداث
        self.sidebar.item_clicked.connect(self.handle_sidebar_click)
        
    def setup_style(self):
        """إعداد تصميم النافذة"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:1 #ffffff);
            }
            QDockWidget {
                titlebar-close-icon: none;
                titlebar-normal-icon: none;
            }
            QDockWidget::title {
                background: transparent;
                text-align: center;
                padding: 10px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
    def handle_sidebar_click(self, item_name):
        """معالجة النقر على عناصر الشريط الجانبي"""
        self.status_bar.showMessage(f"تم اختيار: {item_name}")
        
    def add_page(self, widget, name):
        """إضافة صفحة جديدة"""
        self.central_widget.addWidget(widget)
        
    def show_page(self, index):
        """عرض صفحة محددة"""
        self.central_widget.setCurrentIndex(index)
