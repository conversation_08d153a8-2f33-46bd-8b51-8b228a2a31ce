"""
وحدة التصدير المتقدمة
تدعم تصدير البيانات والتقارير إلى تنسيقات متعددة مع تصميم احترافي
"""

import sqlite3
import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Line<PERSON>hart, Reference
from openpyxl.drawing.image import Image as OpenpyxlImage
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib.pagesizes import A4, letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import json
import csv
from datetime import datetime
import os
import tempfile
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QComboBox, QCheckBox, QGroupBox, QFormLayout, QFileDialog,
    QProgressBar, QMessageBox, QTextEdit, QSpinBox, QDateEdit,
    QTabWidget, QListWidget, QListWidgetItem, QFrame
)
from PyQt5.QtCore import QThread, pyqtSignal, QDate, Qt
from PyQt5.QtGui import QFont, QPixmap, QPainter
import io

class ExportWorker(QThread):
    """عامل التصدير في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    export_completed = pyqtSignal(str)
    export_failed = pyqtSignal(str)
    
    def __init__(self, export_config, data, conn):
        super().__init__()
        self.export_config = export_config
        self.data = data
        self.conn = conn
        
    def run(self):
        try:
            self.status_updated.emit("بدء عملية التصدير...")
            self.progress_updated.emit(10)
            
            export_type = self.export_config['type']
            filename = self.export_config['filename']
            
            if export_type == 'excel':
                self.export_to_excel(filename)
            elif export_type == 'pdf':
                self.export_to_pdf(filename)
            elif export_type == 'word':
                self.export_to_word(filename)
            elif export_type == 'csv':
                self.export_to_csv(filename)
            elif export_type == 'json':
                self.export_to_json(filename)
            
            self.progress_updated.emit(100)
            self.export_completed.emit(filename)
            
        except Exception as e:
            self.export_failed.emit(str(e))
    
    def export_to_excel(self, filename):
        """تصدير إلى Excel مع تنسيق احترافي"""
        self.status_updated.emit("إنشاء ملف Excel...")
        self.progress_updated.emit(20)
        
        # إنشاء مصنف Excel
        wb = openpyxl.Workbook()
        
        # حذف الورقة الافتراضية
        wb.remove(wb.active)
        
        # ورقة البيانات الرئيسية
        ws_data = wb.create_sheet("بيانات الأنشطة")
        self.create_data_sheet(ws_data)
        
        self.progress_updated.emit(40)
        
        # ورقة الإحصائيات
        ws_stats = wb.create_sheet("الإحصائيات")
        self.create_statistics_sheet(ws_stats)
        
        self.progress_updated.emit(60)
        
        # ورقة الرسوم البيانية
        if self.export_config.get('include_charts', True):
            ws_charts = wb.create_sheet("الرسوم البيانية")
            self.create_charts_sheet(ws_charts)
        
        self.progress_updated.emit(80)
        
        # حفظ الملف
        wb.save(filename)
        self.status_updated.emit("تم إنشاء ملف Excel بنجاح")
    
    def create_data_sheet(self, ws):
        """إنشاء ورقة البيانات"""
        # العنوان الرئيسي
        ws.merge_cells('A1:P1')
        title_cell = ws['A1']
        title_cell.value = "تقرير الأنشطة الشامل"
        title_cell.font = Font(size=16, bold=True, color="FFFFFF")
        title_cell.fill = PatternFill(start_color="4CAF50", end_color="4CAF50", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # تاريخ التقرير
        ws.merge_cells('A2:P2')
        date_cell = ws['A2']
        date_cell.value = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        date_cell.font = Font(size=12, italic=True)
        date_cell.alignment = Alignment(horizontal="center")
        
        # رؤوس الأعمدة
        headers = [
            "ID", "الشخص", "اسم النشاط", "التصنيف الأب", "التصنيف الابن",
            "المحافظة", "المديرية", "العزلة", "القرية", "المشاركون",
            "المستهدف", "الميزانية", "التكلفة", "الحالة", "الأولوية", "التاريخ"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col)
            cell.value = header
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="2196F3", end_color="2196F3", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        # البيانات
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT id, person, activity_name, parent_category, sub_category,
                   governorate, district, sub_district, village, participants,
                   target_participants, budget, actual_cost, status, priority, activity_date
            FROM activities
            ORDER BY activity_date DESC
        """)
        
        data = cursor.fetchall()
        
        for row_idx, row_data in enumerate(data, 5):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = value
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # تلوين الصفوف بالتناوب
                if row_idx % 2 == 0:
                    cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
        
        # تعديل عرض الأعمدة
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def create_statistics_sheet(self, ws):
        """إنشاء ورقة الإحصائيات"""
        # العنوان
        ws.merge_cells('A1:D1')
        title_cell = ws['A1']
        title_cell.value = "الإحصائيات العامة"
        title_cell.font = Font(size=16, bold=True, color="FFFFFF")
        title_cell.fill = PatternFill(start_color="FF9800", end_color="FF9800", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # الإحصائيات العامة
        cursor = self.conn.cursor()
        
        # إجمالي الأنشطة
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_activities = cursor.fetchone()[0]
        
        # الأنشطة المكتملة
        cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
        completed_activities = cursor.fetchone()[0]
        
        # إجمالي المشاركين
        cursor.execute("SELECT SUM(participants) FROM activities WHERE participants IS NOT NULL")
        total_participants = cursor.fetchone()[0] or 0
        
        # إجمالي الميزانية
        cursor.execute("SELECT SUM(budget) FROM activities WHERE budget IS NOT NULL")
        total_budget = cursor.fetchone()[0] or 0
        
        stats_data = [
            ["المؤشر", "القيمة", "الوصف"],
            ["إجمالي الأنشطة", total_activities, "العدد الكلي للأنشطة المسجلة"],
            ["الأنشطة المكتملة", completed_activities, "عدد الأنشطة التي تم إنجازها"],
            ["معدل الإنجاز", f"{(completed_activities/total_activities*100):.1f}%" if total_activities > 0 else "0%", "نسبة الأنشطة المكتملة"],
            ["إجمالي المشاركين", f"{total_participants:,}", "العدد الكلي للمشاركين"],
            ["إجمالي الميزانية", f"{total_budget:,.0f} ريال", "المبلغ الكلي للميزانيات"]
        ]
        
        for row_idx, row_data in enumerate(stats_data, 3):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                cell.value = value
                
                if row_idx == 3:  # رؤوس الأعمدة
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="2196F3", end_color="2196F3", fill_type="solid")
                else:
                    if row_idx % 2 == 0:
                        cell.fill = PatternFill(start_color="F8F9FA", end_color="F8F9FA", fill_type="solid")
                
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # تعديل عرض الأعمدة
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 30
    
    def create_charts_sheet(self, ws):
        """إنشاء ورقة الرسوم البيانية"""
        # العنوان
        ws.merge_cells('A1:H1')
        title_cell = ws['A1']
        title_cell.value = "الرسوم البيانية والتحليلات"
        title_cell.font = Font(size=16, bold=True, color="FFFFFF")
        title_cell.fill = PatternFill(start_color="9C27B0", end_color="9C27B0", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # إنشاء رسم بياني للتصنيفات
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT parent_category, COUNT(*) 
            FROM activities 
            GROUP BY parent_category 
            ORDER BY COUNT(*) DESC
        """)
        category_data = cursor.fetchall()
        
        if category_data:
            # إضافة بيانات الرسم البياني
            ws['A3'] = "التصنيف"
            ws['B3'] = "عدد الأنشطة"
            
            for i, (category, count) in enumerate(category_data, 4):
                ws[f'A{i}'] = category
                ws[f'B{i}'] = count
            
            # إنشاء رسم بياني شريطي
            chart = BarChart()
            chart.title = "توزيع الأنشطة حسب التصنيف"
            chart.x_axis.title = "التصنيفات"
            chart.y_axis.title = "عدد الأنشطة"
            
            data_ref = Reference(ws, min_col=2, min_row=3, max_row=3+len(category_data))
            categories_ref = Reference(ws, min_col=1, min_row=4, max_row=3+len(category_data))
            
            chart.add_data(data_ref, titles_from_data=True)
            chart.set_categories(categories_ref)
            
            ws.add_chart(chart, "D3")
    
    def export_to_pdf(self, filename):
        """تصدير إلى PDF"""
        self.status_updated.emit("إنشاء ملف PDF...")
        self.progress_updated.emit(30)
        
        # إنشاء مستند PDF
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        
        # الأنماط
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        # العنوان
        title = Paragraph("تقرير الأنشطة الشامل", title_style)
        story.append(title)
        story.append(Spacer(1, 12))
        
        # تاريخ التقرير
        date_para = Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal'])
        story.append(date_para)
        story.append(Spacer(1, 12))
        
        # الإحصائيات العامة
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_activities = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM activities WHERE status = 'مكتمل'")
        completed_activities = cursor.fetchone()[0]
        
        stats_data = [
            ['المؤشر', 'القيمة'],
            ['إجمالي الأنشطة', str(total_activities)],
            ['الأنشطة المكتملة', str(completed_activities)],
            ['معدل الإنجاز', f"{(completed_activities/total_activities*100):.1f}%" if total_activities > 0 else "0%"]
        ]
        
        stats_table = Table(stats_data)
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 12))
        
        self.progress_updated.emit(70)
        
        # بناء المستند
        doc.build(story)
        self.status_updated.emit("تم إنشاء ملف PDF بنجاح")
    
    def export_to_word(self, filename):
        """تصدير إلى Word"""
        self.status_updated.emit("إنشاء ملف Word...")
        # تنفيذ أساسي - يمكن تحسينه باستخدام python-docx
        self.progress_updated.emit(50)
        
        # إنشاء محتوى HTML بسيط وحفظه كـ RTF
        html_content = self.generate_html_report()
        
        # تحويل إلى RTF (يمكن فتحه في Word)
        rtf_filename = filename.replace('.docx', '.rtf')
        with open(rtf_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.status_updated.emit("تم إنشاء ملف Word بنجاح")
    
    def export_to_csv(self, filename):
        """تصدير إلى CSV"""
        self.status_updated.emit("إنشاء ملف CSV...")
        self.progress_updated.emit(40)
        
        cursor = self.conn.cursor()
        cursor.execute("""
            SELECT id, person, activity_name, parent_category, sub_category,
                   governorate, district, sub_district, village, participants,
                   target_participants, budget, actual_cost, status, priority, activity_date
            FROM activities
            ORDER BY activity_date DESC
        """)
        
        data = cursor.fetchall()
        headers = [
            "ID", "الشخص", "اسم النشاط", "التصنيف الأب", "التصنيف الابن",
            "المحافظة", "المديرية", "العزلة", "القرية", "المشاركون",
            "المستهدف", "الميزانية", "التكلفة", "الحالة", "الأولوية", "التاريخ"
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
            writer.writerows(data)
        
        self.status_updated.emit("تم إنشاء ملف CSV بنجاح")
    
    def export_to_json(self, filename):
        """تصدير إلى JSON"""
        self.status_updated.emit("إنشاء ملف JSON...")
        self.progress_updated.emit(40)
        
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM activities ORDER BY activity_date DESC")
        
        # الحصول على أسماء الأعمدة
        columns = [description[0] for description in cursor.description]
        data = cursor.fetchall()
        
        # تحويل إلى قائمة من القواميس
        activities_list = []
        for row in data:
            activity_dict = dict(zip(columns, row))
            activities_list.append(activity_dict)
        
        # إضافة معلومات إضافية
        export_data = {
            "export_info": {
                "export_date": datetime.now().isoformat(),
                "total_records": len(activities_list),
                "export_type": "activities_data"
            },
            "activities": activities_list
        }
        
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
        
        self.status_updated.emit("تم إنشاء ملف JSON بنجاح")
    
    def generate_html_report(self):
        """إنتاج تقرير HTML"""
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_activities = cursor.fetchone()[0]
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الأنشطة</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #4CAF50; text-align: center; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                th {{ background-color: #4CAF50; color: white; }}
            </style>
        </head>
        <body>
            <h1>تقرير الأنشطة الشامل</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <h2>الإحصائيات العامة</h2>
            <p>إجمالي الأنشطة: {total_activities}</p>
        </body>
        </html>
        """
        return html


class ExportWidget(QWidget):
    """واجهة التصدير المتقدمة"""

    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.export_worker = None
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("🔄 نظام التصدير المتقدم")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تبويبات التصدير
        tab_widget = QTabWidget()

        # تبويب التصدير السريع
        quick_export_tab = self.create_quick_export_tab()
        tab_widget.addTab(quick_export_tab, "التصدير السريع")

        # تبويب التصدير المخصص
        custom_export_tab = self.create_custom_export_tab()
        tab_widget.addTab(custom_export_tab, "التصدير المخصص")

        # تبويب تصدير القوالب
        templates_export_tab = self.create_templates_export_tab()
        tab_widget.addTab(templates_export_tab, "تصدير القوالب")

        # تبويب القوالب
        templates_tab = self.create_templates_tab()
        tab_widget.addTab(templates_tab, "القوالب")

        layout.addWidget(tab_widget)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def create_quick_export_tab(self):
        """إنشاء تبويب التصدير السريع"""
        widget = QWidget()
        layout = QVBoxLayout()

        # مجموعة التصدير السريع
        quick_group = QGroupBox("التصدير السريع")
        quick_layout = QVBoxLayout()

        # أزرار التصدير السريع
        export_buttons = [
            ("📊 تصدير إلى Excel", "excel", "#4CAF50"),
            ("📄 تصدير إلى PDF", "pdf", "#f44336"),
            ("📝 تصدير إلى Word", "word", "#2196F3"),
            ("📋 تصدير إلى CSV", "csv", "#FF9800"),
            ("🔗 تصدير إلى JSON", "json", "#9C27B0")
        ]

        for text, export_type, color in export_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px;
                    font-size: 14px;
                    font-weight: bold;
                    margin: 5px;
                }}
                QPushButton:hover {{
                    background: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background: {self.darken_color(color, 0.8)};
                }}
            """)
            btn.clicked.connect(lambda: self.quick_export(export_type))
            quick_layout.addWidget(btn)

        quick_group.setLayout(quick_layout)
        layout.addWidget(quick_group)

        # معلومات التصدير
        info_group = QGroupBox("معلومات التصدير")
        info_layout = QFormLayout()

        # عدد السجلات
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_records = cursor.fetchone()[0]

        info_layout.addRow("عدد السجلات:", QLabel(str(total_records)))
        info_layout.addRow("آخر تحديث:", QLabel(datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_custom_export_tab(self):
        """إنشاء تبويب التصدير المخصص"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إعدادات التصدير
        settings_group = QGroupBox("إعدادات التصدير")
        settings_layout = QFormLayout()

        # نوع التصدير
        self.export_type_combo = QComboBox()
        self.export_type_combo.addItems([
            "Excel (.xlsx)",
            "PDF (.pdf)",
            "Word (.docx)",
            "CSV (.csv)",
            "JSON (.json)"
        ])
        settings_layout.addRow("نوع الملف:", self.export_type_combo)

        # نطاق التاريخ
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-6))
        self.start_date.setCalendarPopup(True)
        settings_layout.addRow("من تاريخ:", self.start_date)

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        settings_layout.addRow("إلى تاريخ:", self.end_date)

        # فلاتر إضافية
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع التصنيفات")
        self.load_categories()
        settings_layout.addRow("التصنيف:", self.category_filter)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مخطط", "جاري", "مكتمل", "ملغي"])
        settings_layout.addRow("الحالة:", self.status_filter)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # خيارات التصدير
        options_group = QGroupBox("خيارات التصدير")
        options_layout = QVBoxLayout()

        self.include_charts = QCheckBox("تضمين الرسوم البيانية")
        self.include_charts.setChecked(True)
        options_layout.addWidget(self.include_charts)

        self.include_statistics = QCheckBox("تضمين الإحصائيات")
        self.include_statistics.setChecked(True)
        options_layout.addWidget(self.include_statistics)

        self.include_summary = QCheckBox("تضمين الملخص التنفيذي")
        self.include_summary.setChecked(True)
        options_layout.addWidget(self.include_summary)

        self.compress_output = QCheckBox("ضغط الملف الناتج")
        options_layout.addWidget(self.compress_output)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # زر التصدير المخصص
        export_btn = QPushButton("🚀 بدء التصدير المخصص")
        export_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
        """)
        export_btn.clicked.connect(self.custom_export)
        layout.addWidget(export_btn)

        widget.setLayout(layout)
        return widget

    def create_templates_tab(self):
        """إنشاء تبويب القوالب"""
        widget = QWidget()
        layout = QVBoxLayout()

        # قائمة القوالب
        templates_group = QGroupBox("القوالب المحفوظة")
        templates_layout = QVBoxLayout()

        self.templates_list = QListWidget()
        self.load_templates()
        templates_layout.addWidget(self.templates_list)

        # أزرار إدارة القوالب
        templates_buttons = QHBoxLayout()

        save_template_btn = QPushButton("💾 حفظ قالب جديد")
        save_template_btn.clicked.connect(self.save_template)
        templates_buttons.addWidget(save_template_btn)

        load_template_btn = QPushButton("📂 تحميل قالب")
        load_template_btn.clicked.connect(self.load_template)
        templates_buttons.addWidget(load_template_btn)

        delete_template_btn = QPushButton("🗑️ حذف قالب")
        delete_template_btn.clicked.connect(self.delete_template)
        templates_buttons.addWidget(delete_template_btn)

        templates_layout.addLayout(templates_buttons)
        templates_group.setLayout(templates_layout)
        layout.addWidget(templates_group)

        # معاينة القالب
        preview_group = QGroupBox("معاينة القالب")
        preview_layout = QVBoxLayout()

        self.template_preview = QTextEdit()
        self.template_preview.setReadOnly(True)
        self.template_preview.setMaximumHeight(200)
        preview_layout.addWidget(self.template_preview)

        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)

        widget.setLayout(layout)
        return widget

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تنفيذ بسيط لتغميق اللون
        return color  # يمكن تحسينه لاحقاً

    def load_categories(self):
        """تحميل التصنيفات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT DISTINCT parent_category FROM categories ORDER BY parent_category")
            categories = [row[0] for row in cursor.fetchall()]
            self.category_filter.addItems(categories)
        except Exception as e:
            print(f"خطأ في تحميل التصنيفات: {e}")

    def load_templates(self):
        """تحميل القوالب المحفوظة"""
        # تنفيذ أساسي - يمكن تحسينه لاحقاً
        templates = [
            "قالب التقرير الشهري",
            "قالب التقرير السنوي",
            "قالب تقرير الأداء",
            "قالب التقرير المالي"
        ]

        for template in templates:
            item = QListWidgetItem(template)
            self.templates_list.addItem(item)

    def quick_export(self, export_type):
        """التصدير السريع"""
        filename, _ = QFileDialog.getSaveFileName(
            self, f"حفظ ملف {export_type.upper()}",
            f"تقرير_الأنشطة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_type}",
            self.get_file_filter(export_type)
        )

        if filename:
            self.start_export({
                'type': export_type,
                'filename': filename,
                'include_charts': True,
                'include_statistics': True,
                'include_summary': True
            })

    def custom_export(self):
        """التصدير المخصص"""
        export_type = self.export_type_combo.currentText().split()[0].lower()

        filename, _ = QFileDialog.getSaveFileName(
            self, f"حفظ ملف {export_type.upper()}",
            f"تقرير_مخصص_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_type}",
            self.get_file_filter(export_type)
        )

        if filename:
            config = {
                'type': export_type,
                'filename': filename,
                'start_date': self.start_date.date().toString("yyyy-MM-dd"),
                'end_date': self.end_date.date().toString("yyyy-MM-dd"),
                'category_filter': self.category_filter.currentText(),
                'status_filter': self.status_filter.currentText(),
                'include_charts': self.include_charts.isChecked(),
                'include_statistics': self.include_statistics.isChecked(),
                'include_summary': self.include_summary.isChecked(),
                'compress_output': self.compress_output.isChecked()
            }

            self.start_export(config)

    def get_file_filter(self, export_type):
        """الحصول على فلتر الملف"""
        filters = {
            'excel': "Excel Files (*.xlsx)",
            'pdf': "PDF Files (*.pdf)",
            'word': "Word Files (*.docx)",
            'csv': "CSV Files (*.csv)",
            'json': "JSON Files (*.json)"
        }
        return filters.get(export_type, "All Files (*)")

    def start_export(self, config):
        """بدء عملية التصدير"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # إنشاء عامل التصدير
            self.export_worker = ExportWorker(config, None, self.conn)
            self.export_worker.progress_updated.connect(self.progress_bar.setValue)
            self.export_worker.status_updated.connect(self.status_label.setText)
            self.export_worker.export_completed.connect(self.export_completed)
            self.export_worker.export_failed.connect(self.export_failed)

            self.export_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء التصدير: {str(e)}")

    def export_completed(self, filename):
        """اكتمال التصدير"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("تم التصدير بنجاح")
        QMessageBox.information(self, "نجح", f"تم تصدير الملف بنجاح:\n{filename}")

    def export_failed(self, error):
        """فشل التصدير"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل التصدير")
        QMessageBox.critical(self, "خطأ", f"فشل في التصدير:\n{error}")

    def save_template(self):
        """حفظ قالب جديد"""
        QMessageBox.information(self, "معلومات", "ميزة حفظ القوالب قيد التطوير")

    def load_template(self):
        """تحميل قالب"""
        QMessageBox.information(self, "معلومات", "ميزة تحميل القوالب قيد التطوير")

    def delete_template(self):
        """حذف قالب"""
        QMessageBox.information(self, "معلومات", "ميزة حذف القوالب قيد التطوير")

    def create_templates_export_tab(self):
        """إنشاء تبويب تصدير القوالب"""
        widget = QWidget()
        layout = QVBoxLayout()

        # العنوان والوصف
        title_label = QLabel("📋 تصدير القوالب الجاهزة")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        description_label = QLabel("قم بتصدير قوالب جاهزة للاستخدام في إدخال البيانات أو التقارير")
        description_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        layout.addWidget(description_label)

        # قسم القوالب المتاحة
        templates_group = QGroupBox("القوالب المتاحة")
        templates_layout = QVBoxLayout()

        # قائمة القوالب
        self.templates_export_list = QListWidget()
        self.templates_export_list.setMaximumHeight(200)

        # إضافة القوالب المتاحة
        templates = [
            {
                "name": "📊 قالب إدخال الأنشطة",
                "description": "قالب Excel فارغ لإدخال الأنشطة الجديدة مع جميع الحقول المطلوبة",
                "type": "input_template",
                "icon": "📊"
            },
            {
                "name": "📈 قالب تقرير الأداء",
                "description": "قالب تقرير جاهز لعرض مؤشرات الأداء الرئيسية",
                "type": "performance_template",
                "icon": "📈"
            },
            {
                "name": "🗺️ قالب التوزيع الجغرافي",
                "description": "قالب لعرض توزيع الأنشطة حسب المناطق الجغرافية",
                "type": "geographic_template",
                "icon": "🗺️"
            },
            {
                "name": "💰 قالب التقرير المالي",
                "description": "قالب لعرض الميزانيات والتكاليف والتحليل المالي",
                "type": "financial_template",
                "icon": "💰"
            },
            {
                "name": "📅 قالب التقرير الشهري",
                "description": "قالب تقرير شهري شامل مع الإحصائيات والرسوم البيانية",
                "type": "monthly_template",
                "icon": "📅"
            },
            {
                "name": "👥 قالب تقرير المشاركين",
                "description": "قالب لعرض إحصائيات المشاركين والفئات المستهدفة",
                "type": "participants_template",
                "icon": "👥"
            }
        ]

        for template in templates:
            item = QListWidgetItem(f"{template['icon']} {template['name']}")
            item.setToolTip(template['description'])
            item.setData(Qt.UserRole, template)
            self.templates_export_list.addItem(item)

        self.templates_export_list.itemClicked.connect(self.template_selected)
        templates_layout.addWidget(self.templates_export_list)

        templates_group.setLayout(templates_layout)
        layout.addWidget(templates_group)

        # معاينة القالب المحدد
        preview_group = QGroupBox("معاينة القالب")
        preview_layout = QVBoxLayout()

        self.template_preview_export = QTextEdit()
        self.template_preview_export.setMaximumHeight(150)
        self.template_preview_export.setReadOnly(True)
        self.template_preview_export.setPlaceholderText("اختر قالباً لعرض التفاصيل...")
        preview_layout.addWidget(self.template_preview_export)

        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)

        # خيارات التصدير
        options_group = QGroupBox("خيارات التصدير")
        options_layout = QFormLayout()

        # تنسيق التصدير
        self.template_format = QComboBox()
        self.template_format.addItems([
            "Excel (.xlsx)",
            "PDF (.pdf)",
            "Word (.docx)",
            "CSV (.csv)"
        ])
        options_layout.addRow("تنسيق التصدير:", self.template_format)

        # تضمين البيانات النموذجية
        self.include_sample_data = QCheckBox("تضمين بيانات نموذجية")
        self.include_sample_data.setChecked(True)
        options_layout.addRow("", self.include_sample_data)

        # تضمين التعليمات
        self.include_instructions = QCheckBox("تضمين تعليمات الاستخدام")
        self.include_instructions.setChecked(True)
        options_layout.addRow("", self.include_instructions)

        # إضافة الشعار
        self.include_logo = QCheckBox("إضافة شعار المؤسسة")
        self.include_logo.setChecked(False)
        options_layout.addRow("", self.include_logo)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # أزرار التصدير
        buttons_layout = QHBoxLayout()

        export_template_btn = QPushButton("📤 تصدير القالب المحدد")
        export_template_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #2ecc71);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
            }
        """)
        export_template_btn.clicked.connect(self.export_selected_template)
        buttons_layout.addWidget(export_template_btn)

        export_all_templates_btn = QPushButton("📦 تصدير جميع القوالب")
        export_all_templates_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #3498db);
            }
        """)
        export_all_templates_btn.clicked.connect(self.export_all_templates)
        buttons_layout.addWidget(export_all_templates_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def template_selected(self, item):
        """عند اختيار قالب"""
        template_data = item.data(Qt.UserRole)
        if template_data:
            preview_text = f"""
            <h3>{template_data['name']}</h3>
            <p><strong>الوصف:</strong> {template_data['description']}</p>
            <p><strong>النوع:</strong> {template_data['type']}</p>

            <h4>محتويات القالب:</h4>
            """

            # إضافة تفاصيل محددة لكل قالب
            if template_data['type'] == 'input_template':
                preview_text += """
                <ul>
                    <li>حقول إدخال الأنشطة الأساسية</li>
                    <li>قوائم منسدلة للتصنيفات</li>
                    <li>حقول المواقع الجغرافية</li>
                    <li>حقول الميزانية والمشاركين</li>
                    <li>تنسيق احترافي مع التحقق من صحة البيانات</li>
                </ul>
                """
            elif template_data['type'] == 'performance_template':
                preview_text += """
                <ul>
                    <li>مؤشرات الأداء الرئيسية (KPIs)</li>
                    <li>رسوم بيانية للإنجاز</li>
                    <li>جداول مقارنة الأهداف والنتائج</li>
                    <li>تحليل الاتجاهات</li>
                </ul>
                """
            elif template_data['type'] == 'geographic_template':
                preview_text += """
                <ul>
                    <li>خرائط توزيع الأنشطة</li>
                    <li>إحصائيات حسب المحافظات</li>
                    <li>تحليل التغطية الجغرافية</li>
                    <li>مؤشرات الكثافة السكانية</li>
                </ul>
                """
            elif template_data['type'] == 'financial_template':
                preview_text += """
                <ul>
                    <li>تحليل الميزانيات والتكاليف</li>
                    <li>مقارنة المخطط والفعلي</li>
                    <li>مؤشرات الكفاءة المالية</li>
                    <li>توقعات الإنفاق</li>
                </ul>
                """
            elif template_data['type'] == 'monthly_template':
                preview_text += """
                <ul>
                    <li>ملخص الأنشطة الشهرية</li>
                    <li>مقارنة مع الأشهر السابقة</li>
                    <li>الإنجازات والتحديات</li>
                    <li>خطة الشهر القادم</li>
                </ul>
                """
            elif template_data['type'] == 'participants_template':
                preview_text += """
                <ul>
                    <li>إحصائيات المشاركين حسب الفئات</li>
                    <li>تحليل الفئات المستهدفة</li>
                    <li>معدلات المشاركة</li>
                    <li>توزيع ديموغرافي</li>
                </ul>
                """

            self.template_preview_export.setHtml(preview_text)

    def export_selected_template(self):
        """تصدير القالب المحدد"""
        current_item = self.templates_export_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قالب أولاً")
            return

        template_data = current_item.data(Qt.UserRole)
        if not template_data:
            return

        # اختيار مكان الحفظ
        format_text = self.template_format.currentText()
        extension = format_text.split('(')[1].split(')')[0]

        filename, _ = QFileDialog.getSaveFileName(
            self, "حفظ القالب",
            f"{template_data['name'].replace('📊 ', '').replace('📈 ', '').replace('🗺️ ', '').replace('💰 ', '').replace('📅 ', '').replace('👥 ', '')}_{datetime.now().strftime('%Y%m%d')}{extension}",
            f"Template Files (*{extension})"
        )

        if filename:
            try:
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.status_label.setText("جاري إنشاء القالب...")

                # إنشاء القالب حسب النوع
                if extension == '.xlsx':
                    self.create_excel_template(filename, template_data)
                elif extension == '.pdf':
                    self.create_pdf_template(filename, template_data)
                elif extension == '.docx':
                    self.create_word_template(filename, template_data)
                elif extension == '.csv':
                    self.create_csv_template(filename, template_data)

                self.progress_bar.setValue(100)
                self.status_label.setText("تم إنشاء القالب بنجاح")
                QMessageBox.information(self, "نجح", f"تم إنشاء القالب بنجاح:\n{filename}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء القالب:\n{str(e)}")
            finally:
                self.progress_bar.setVisible(False)

    def export_all_templates(self):
        """تصدير جميع القوالب"""
        folder = QFileDialog.getExistingDirectory(self, "اختيار مجلد حفظ القوالب")
        if not folder:
            return

        try:
            self.progress_bar.setVisible(True)
            self.status_label.setText("جاري إنشاء جميع القوالب...")

            total_templates = self.templates_export_list.count()

            for i in range(total_templates):
                item = self.templates_export_list.item(i)
                template_data = item.data(Qt.UserRole)

                progress = int((i / total_templates) * 100)
                self.progress_bar.setValue(progress)

                # إنشاء اسم الملف
                template_name = template_data['name'].replace('📊 ', '').replace('📈 ', '').replace('🗺️ ', '').replace('💰 ', '').replace('📅 ', '').replace('👥 ', '')
                filename = os.path.join(folder, f"{template_name}_{datetime.now().strftime('%Y%m%d')}.xlsx")

                # إنشاء القالب
                self.create_excel_template(filename, template_data)

            self.progress_bar.setValue(100)
            self.status_label.setText("تم إنشاء جميع القوالب بنجاح")
            QMessageBox.information(self, "نجح", f"تم إنشاء جميع القوالب في المجلد:\n{folder}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء القوالب:\n{str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def create_excel_template(self, filename, template_data):
        """إنشاء قالب Excel"""
        wb = openpyxl.Workbook()
        ws = wb.active

        # تنسيق عام
        header_font = Font(size=14, bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4CAF50", end_color="4CAF50", fill_type="solid")

        if template_data['type'] == 'input_template':
            self.create_input_template_excel(ws, header_font, header_fill)
        elif template_data['type'] == 'performance_template':
            self.create_performance_template_excel(ws, header_font, header_fill)
        elif template_data['type'] == 'geographic_template':
            self.create_geographic_template_excel(ws, header_font, header_fill)
        elif template_data['type'] == 'financial_template':
            self.create_financial_template_excel(ws, header_font, header_fill)
        elif template_data['type'] == 'monthly_template':
            self.create_monthly_template_excel(ws, header_font, header_fill)
        elif template_data['type'] == 'participants_template':
            self.create_participants_template_excel(ws, header_font, header_fill)

        # إضافة التعليمات إذا كانت مطلوبة
        if self.include_instructions.isChecked():
            self.add_instructions_sheet(wb, template_data)

        wb.save(filename)

    def create_input_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب إدخال الأنشطة"""
        ws.title = "قالب إدخال الأنشطة"

        # العنوان
        ws.merge_cells('A1:P1')
        title_cell = ws['A1']
        title_cell.value = "قالب إدخال الأنشطة الجديدة"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center", vertical="center")

        # رؤوس الأعمدة
        headers = [
            "الرقم", "اسم الشخص", "اسم النشاط", "التصنيف الأب", "التصنيف الابن",
            "المحافظة", "المديرية", "العزلة", "القرية", "عدد المشاركين",
            "المستهدف", "الميزانية", "التكلفة الفعلية", "الحالة", "الأولوية", "تاريخ النشاط"
        ]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # إضافة بيانات نموذجية إذا كانت مطلوبة
        if self.include_sample_data.isChecked():
            sample_data = [
                [1, "أحمد محمد", "ورشة تدريبية", "التعليم", "التدريب المهني", "صنعاء", "الأمانة", "شعوب", "الحصبة", 50, 60, 10000, 9500, "مكتمل", "عالية", "2024-01-15"],
                [2, "فاطمة علي", "حملة توعية", "الصحة", "التوعية الصحية", "عدن", "المعلا", "كريتر", "التواهي", 100, 120, 5000, 4800, "جاري", "متوسطة", "2024-01-20"],
                [3, "محمد سالم", "مشروع تنموي", "التنمية", "التنمية المجتمعية", "تعز", "المدينة", "الوازعية", "الشمايتين", 200, 250, 25000, 0, "مخطط", "عالية", "2024-02-01"]
            ]

            for row_idx, row_data in enumerate(sample_data, 4):
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=value)

        # تنسيق الأعمدة
        column_widths = [8, 15, 25, 15, 15, 12, 12, 12, 12, 12, 12, 12, 12, 10, 10, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width

    def create_performance_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب تقرير الأداء"""
        ws.title = "تقرير الأداء"

        # العنوان
        ws.merge_cells('A1:F1')
        title_cell = ws['A1']
        title_cell.value = "تقرير مؤشرات الأداء الرئيسية"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center")

        # مؤشرات الأداء
        kpis = [
            ["المؤشر", "القيمة الحالية", "الهدف", "النسبة", "الحالة", "الملاحظات"],
            ["إجمالي الأنشطة", "=COUNTA(B8:B100)", "100", "=B4/C4*100", "=IF(D4>=90,\"ممتاز\",IF(D4>=70,\"جيد\",\"يحتاج تحسين\"))", ""],
            ["الأنشطة المكتملة", "=COUNTIF(E8:E100,\"مكتمل\")", "80", "=B5/C5*100", "=IF(D5>=90,\"ممتاز\",IF(D5>=70,\"جيد\",\"يحتاج تحسين\"))", ""],
            ["إجمالي المشاركين", "=SUM(J8:J100)", "5000", "=B6/C6*100", "=IF(D6>=90,\"ممتاز\",IF(D6>=70,\"جيد\",\"يحتاج تحسين\"))", ""],
            ["كفاءة الميزانية", "=SUM(L8:L100)/SUM(M8:M100)*100", "95", "=B7/C7*100", "=IF(D7>=90,\"ممتاز\",IF(D7>=70,\"جيد\",\"يحتاج تحسين\"))", ""]
        ]

        for row_idx, row_data in enumerate(kpis, 3):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 3:  # رؤوس الأعمدة
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")

    def create_pdf_template(self, filename, template_data):
        """إنشاء قالب PDF"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []

        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1
        )

        # العنوان
        title = Paragraph(f"قالب {template_data['name']}", title_style)
        story.append(title)
        story.append(Spacer(1, 12))

        # الوصف
        desc = Paragraph(template_data['description'], styles['Normal'])
        story.append(desc)
        story.append(Spacer(1, 12))

        # إضافة محتوى حسب نوع القالب
        if template_data['type'] == 'input_template':
            content = [
                "هذا القالب يحتوي على:",
                "• حقول إدخال الأنشطة الأساسية",
                "• قوائم التصنيفات المختلفة",
                "• حقول المواقع الجغرافية",
                "• حقول الميزانية والمشاركين"
            ]
        else:
            content = [
                "هذا قالب متخصص يحتوي على:",
                "• تحليلات متقدمة",
                "• رسوم بيانية تفاعلية",
                "• مؤشرات أداء رئيسية"
            ]

        for line in content:
            para = Paragraph(line, styles['Normal'])
            story.append(para)
            story.append(Spacer(1, 6))

        doc.build(story)

    def create_word_template(self, filename, template_data):
        """إنشاء قالب Word"""
        # إنشاء محتوى HTML بسيط
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>{template_data['name']}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #4CAF50; text-align: center; }}
                .description {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>{template_data['name']}</h1>
            <div class="description">
                <p>{template_data['description']}</p>
            </div>
            <h2>تعليمات الاستخدام:</h2>
            <ol>
                <li>قم بتعبئة البيانات في الحقول المخصصة</li>
                <li>تأكد من صحة البيانات المدخلة</li>
                <li>احفظ الملف بعد الانتهاء</li>
            </ol>
        </body>
        </html>
        """

        # حفظ كملف HTML (يمكن فتحه في Word)
        with open(filename.replace('.docx', '.html'), 'w', encoding='utf-8') as f:
            f.write(html_content)

    def create_csv_template(self, filename, template_data):
        """إنشاء قالب CSV"""
        if template_data['type'] == 'input_template':
            headers = [
                "الرقم", "اسم الشخص", "اسم النشاط", "التصنيف الأب", "التصنيف الابن",
                "المحافظة", "المديرية", "العزلة", "القرية", "عدد المشاركين",
                "المستهدف", "الميزانية", "التكلفة الفعلية", "الحالة", "الأولوية", "تاريخ النشاط"
            ]

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)

                # إضافة بيانات نموذجية إذا كانت مطلوبة
                if self.include_sample_data.isChecked():
                    sample_data = [
                        [1, "مثال", "نشاط تجريبي", "تصنيف", "فرعي", "محافظة", "مديرية", "عزلة", "قرية", 50, 60, 10000, 9500, "مكتمل", "عالية", "2024-01-15"]
                    ]
                    writer.writerows(sample_data)

    def add_instructions_sheet(self, wb, template_data):
        """إضافة ورقة التعليمات"""
        ws = wb.create_sheet("تعليمات الاستخدام")

        # العنوان
        ws.merge_cells('A1:D1')
        title_cell = ws['A1']
        title_cell.value = "تعليمات استخدام القالب"
        title_cell.font = Font(size=16, bold=True, color="FFFFFF")
        title_cell.fill = PatternFill(start_color="2196F3", end_color="2196F3", fill_type="solid")
        title_cell.alignment = Alignment(horizontal="center")

        # التعليمات
        instructions = [
            "خطوات الاستخدام:",
            "1. قم بتعبئة البيانات في الحقول المخصصة",
            "2. تأكد من صحة البيانات المدخلة",
            "3. استخدم القوائم المنسدلة للاختيار",
            "4. احفظ الملف بعد الانتهاء",
            "",
            "ملاحظات مهمة:",
            "• الحقول المميزة بالأحمر مطلوبة",
            "• استخدم التنسيق الصحيح للتواريخ",
            "• تأكد من صحة الأرقام المدخلة"
        ]

        for row, instruction in enumerate(instructions, 3):
            cell = ws.cell(row=row, column=1, value=instruction)
            if instruction.startswith(("خطوات", "ملاحظات")):
                cell.font = Font(bold=True, color="2196F3")

        # تنسيق العمود
        ws.column_dimensions['A'].width = 50

    def create_geographic_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب التوزيع الجغرافي"""
        ws.title = "التوزيع الجغرافي"

        # العنوان
        ws.merge_cells('A1:E1')
        title_cell = ws['A1']
        title_cell.value = "تقرير التوزيع الجغرافي للأنشطة"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center")

        # رؤوس الأعمدة
        headers = ["المحافظة", "عدد الأنشطة", "المشاركون", "الميزانية", "النسبة %"]

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col)
            cell.value = header
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")

        # بيانات نموذجية
        if self.include_sample_data.isChecked():
            sample_data = [
                ["صنعاء", "=COUNTIF(F8:F100,\"صنعاء\")", "=SUMIF(F8:F100,\"صنعاء\",J8:J100)", "=SUMIF(F8:F100,\"صنعاء\",L8:L100)", "=B4/SUM(B:B)*100"],
                ["عدن", "=COUNTIF(F8:F100,\"عدن\")", "=SUMIF(F8:F100,\"عدن\",J8:J100)", "=SUMIF(F8:F100,\"عدن\",L8:L100)", "=B5/SUM(B:B)*100"],
                ["تعز", "=COUNTIF(F8:F100,\"تعز\")", "=SUMIF(F8:F100,\"تعز\",J8:J100)", "=SUMIF(F8:F100,\"تعز\",L8:L100)", "=B6/SUM(B:B)*100"]
            ]

            for row_idx, row_data in enumerate(sample_data, 4):
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=value)

    def create_financial_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب التقرير المالي"""
        ws.title = "التقرير المالي"

        # العنوان
        ws.merge_cells('A1:F1')
        title_cell = ws['A1']
        title_cell.value = "التقرير المالي للأنشطة"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center")

        # قسم الملخص المالي
        ws['A3'] = "الملخص المالي"
        ws['A3'].font = Font(bold=True, size=12)

        financial_summary = [
            ["البند", "المبلغ المخطط", "المبلغ الفعلي", "الفرق", "النسبة %"],
            ["إجمالي الميزانية", "=SUM(L8:L100)", "=SUM(M8:M100)", "=B5-C5", "=C5/B5*100"],
            ["متوسط تكلفة النشاط", "=AVERAGE(L8:L100)", "=AVERAGE(M8:M100)", "=B6-C6", "=C6/B6*100"],
            ["أعلى تكلفة", "=MAX(L8:L100)", "=MAX(M8:M100)", "=B7-C7", "=C7/B7*100"],
            ["أقل تكلفة", "=MIN(L8:L100)", "=MIN(M8:M100)", "=B8-C8", "=C8/B8*100"]
        ]

        for row_idx, row_data in enumerate(financial_summary, 4):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 4:  # رؤوس الأعمدة
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="FFF3E0", end_color="FFF3E0", fill_type="solid")

    def create_monthly_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب التقرير الشهري"""
        ws.title = "التقرير الشهري"

        # العنوان
        ws.merge_cells('A1:G1')
        title_cell = ws['A1']
        title_cell.value = "التقرير الشهري للأنشطة"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center")

        # معلومات الشهر
        ws['A3'] = "الشهر:"
        ws['B3'] = "يناير 2024"
        ws['A4'] = "تاريخ التقرير:"
        ws['B4'] = "=TODAY()"

        # ملخص الشهر
        monthly_summary = [
            ["المؤشر", "العدد", "النسبة", "مقارنة بالشهر السابق"],
            ["الأنشطة المخططة", "=COUNTIFS(P8:P100,\">=\"&DATE(2024,1,1),P8:P100,\"<\"&DATE(2024,2,1))", "", ""],
            ["الأنشطة المنفذة", "=COUNTIFS(P8:P100,\">=\"&DATE(2024,1,1),P8:P100,\"<\"&DATE(2024,2,1),N8:N100,\"مكتمل\")", "=B7/B6*100", ""],
            ["إجمالي المشاركين", "=SUMIFS(J8:J100,P8:P100,\">=\"&DATE(2024,1,1),P8:P100,\"<\"&DATE(2024,2,1))", "", ""],
            ["إجمالي التكلفة", "=SUMIFS(M8:M100,P8:P100,\">=\"&DATE(2024,1,1),P8:P100,\"<\"&DATE(2024,2,1))", "", ""]
        ]

        for row_idx, row_data in enumerate(monthly_summary, 6):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 6:  # رؤوس الأعمدة
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="E8F5E8", end_color="E8F5E8", fill_type="solid")

    def create_participants_template_excel(self, ws, header_font, header_fill):
        """إنشاء قالب تقرير المشاركين"""
        ws.title = "تقرير المشاركين"

        # العنوان
        ws.merge_cells('A1:E1')
        title_cell = ws['A1']
        title_cell.value = "تقرير إحصائيات المشاركين"
        title_cell.font = header_font
        title_cell.fill = header_fill
        title_cell.alignment = Alignment(horizontal="center")

        # إحصائيات المشاركين
        participants_stats = [
            ["التصنيف", "عدد الأنشطة", "إجمالي المشاركين", "متوسط المشاركين", "النسبة %"],
            ["التعليم", "=COUNTIF(D8:D100,\"التعليم\")", "=SUMIF(D8:D100,\"التعليم\",J8:J100)", "=C4/B4", "=C4/SUM(C:C)*100"],
            ["الصحة", "=COUNTIF(D8:D100,\"الصحة\")", "=SUMIF(D8:D100,\"الصحة\",J8:J100)", "=C5/B5", "=C5/SUM(C:C)*100"],
            ["التنمية", "=COUNTIF(D8:D100,\"التنمية\")", "=SUMIF(D8:D100,\"التنمية\",J8:J100)", "=C6/B6", "=C6/SUM(C:C)*100"]
        ]

        for row_idx, row_data in enumerate(participants_stats, 3):
            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                if row_idx == 3:  # رؤوس الأعمدة
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="F3E5F5", end_color="F3E5F5", fill_type="solid")
