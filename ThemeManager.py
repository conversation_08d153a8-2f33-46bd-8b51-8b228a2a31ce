"""
مدير السمات - ThemeManager
يوفر هذا الملف وظائف لإدارة سمات واجهة المستخدم وتطبيقها على التطبيق
يتيح للمستخدم اختيار ألوان الواجهة وحجم الخط
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox,
    QColorDialog, QGroupBox, QFormLayout, QSlider, QApplication, QMessageBox,
    QTabWidget, QWidget, QGridLayout
)
from PyQt5.QtCore import Qt, QSettings, QSize
from PyQt5.QtGui import QColor, QFont, QPalette
import json
import os
import ModernDialog

# تعريف السمات الافتراضية
DEFAULT_THEMES = {
    "الافتراضي": {
        "primary_color": "#3498db",
        "secondary_color": "#2980b9",
        "background_color": "#f5f5f5",
        "text_color": "#333333",
        "button_color": "#2ecc71",
        "button_text_color": "#ffffff",
        "accent_color": "#e74c3c",
        "font_size": 10,
        "font_family": "Arial"
    },
    "داكن": {
        "primary_color": "#2c3e50",
        "secondary_color": "#34495e",
        "background_color": "#1a1a1a",
        "text_color": "#ecf0f1",
        "button_color": "#3498db",
        "button_text_color": "#ffffff",
        "accent_color": "#e74c3c",
        "font_size": 10,
        "font_family": "Arial"
    },
    "عالي التباين": {
        "primary_color": "#000000",
        "secondary_color": "#333333",
        "background_color": "#ffffff",
        "text_color": "#000000",
        "button_color": "#000000",
        "button_text_color": "#ffffff",
        "accent_color": "#ff0000",
        "font_size": 12,
        "font_family": "Arial"
    }
}

class ThemeManager:
    """
    مدير السمات - يتيح إدارة وتطبيق سمات واجهة المستخدم
    """
    def __init__(self):
        """تهيئة مدير السمات"""
        self.settings = QSettings("AymanDhafer", "AssetManagement")
        self.current_theme = self.load_current_theme()
        self.themes = self.load_themes()
        
    def load_themes(self):
        """تحميل السمات المخزنة أو استخدام السمات الافتراضية"""
        themes_json = self.settings.value("themes")
        if themes_json:
            try:
                return json.loads(themes_json)
            except:
                return DEFAULT_THEMES
        return DEFAULT_THEMES
    
    def save_themes(self):
        """حفظ السمات في الإعدادات"""
        self.settings.setValue("themes", json.dumps(self.themes))
        
    def load_current_theme(self):
        """تحميل السمة الحالية من الإعدادات"""
        theme_name = self.settings.value("current_theme", "الافتراضي")
        return theme_name
    
    def save_current_theme(self, theme_name):
        """حفظ السمة الحالية في الإعدادات"""
        self.settings.setValue("current_theme", theme_name)
        self.current_theme = theme_name
        
    def get_current_theme_data(self):
        """الحصول على بيانات السمة الحالية"""
        return self.themes.get(self.current_theme, DEFAULT_THEMES["الافتراضي"])
    
    def apply_theme(self, app):
        """تطبيق السمة الحالية على التطبيق"""
        theme_data = self.get_current_theme_data()
        
        # تطبيق الألوان
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(theme_data["background_color"]))
        palette.setColor(QPalette.WindowText, QColor(theme_data["text_color"]))
        palette.setColor(QPalette.Base, QColor(theme_data["background_color"]))
        palette.setColor(QPalette.AlternateBase, QColor(theme_data["secondary_color"]))
        palette.setColor(QPalette.ToolTipBase, QColor(theme_data["background_color"]))
        palette.setColor(QPalette.ToolTipText, QColor(theme_data["text_color"]))
        palette.setColor(QPalette.Text, QColor(theme_data["text_color"]))
        palette.setColor(QPalette.Button, QColor(theme_data["button_color"]))
        palette.setColor(QPalette.ButtonText, QColor(theme_data["button_text_color"]))
        palette.setColor(QPalette.BrightText, Qt.red)
        palette.setColor(QPalette.Link, QColor(theme_data["primary_color"]))
        palette.setColor(QPalette.Highlight, QColor(theme_data["primary_color"]))
        palette.setColor(QPalette.HighlightedText, QColor(theme_data["button_text_color"]))
        
        app.setPalette(palette)
        
        # تطبيق الخط
        font = QFont(theme_data["font_family"], theme_data["font_size"])
        app.setFont(font)
        
        # تطبيق ورقة الأنماط
        style_sheet = f"""
            QWidget {{
                background-color: {theme_data["background_color"]};
                color: {theme_data["text_color"]};
                font-family: {theme_data["font_family"]};
                font-size: {theme_data["font_size"]}pt;
            }}
            
            QPushButton {{
                background-color: {theme_data["button_color"]};
                color: {theme_data["button_text_color"]};
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {theme_data["primary_color"]};
            }}
            
            QLineEdit, QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox {{
                background-color: white;
                color: {theme_data["text_color"]};
                border: 1px solid {theme_data["secondary_color"]};
                border-radius: 4px;
                padding: 4px;
            }}
            
            QTableWidget {{
                alternate-background-color: #f0f0f0;
                gridline-color: #d0d0d0;
            }}
            
            QTableWidget::item:selected {{
                background-color: {theme_data["primary_color"]};
                color: {theme_data["button_text_color"]};
            }}
            
            QHeaderView::section {{
                background-color: {theme_data["secondary_color"]};
                color: {theme_data["button_text_color"]};
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }}
            
            QTabWidget::pane {{
                border: 1px solid {theme_data["secondary_color"]};
                border-radius: 4px;
            }}
            
            QTabBar::tab {{
                background-color: {theme_data["secondary_color"]};
                color: {theme_data["button_text_color"]};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {theme_data["primary_color"]};
            }}
            
            QGroupBox {{
                border: 1px solid {theme_data["secondary_color"]};
                border-radius: 4px;
                margin-top: 16px;
                font-weight: bold;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }}
        """
        
        app.setStyleSheet(style_sheet)
        
    def add_theme(self, name, theme_data):
        """إضافة سمة جديدة"""
        self.themes[name] = theme_data
        self.save_themes()
        
    def remove_theme(self, name):
        """حذف سمة"""
        if name in self.themes and name not in ["الافتراضي", "داكن", "عالي التباين"]:
            del self.themes[name]
            self.save_themes()
            return True
        return False

class ThemeDialog(ModernDialog.ModernDialog):
    """
    نافذة إدارة السمات
    تتيح للمستخدم اختيار وتخصيص سمات واجهة المستخدم
    """
    def __init__(self, parent=None, theme_manager=None, app=None):
        """
        تهيئة نافذة إدارة السمات
        
        المعاملات:
            parent: النافذة الأم
            theme_manager: مدير السمات
            app: تطبيق PyQt
        """
        super().__init__(parent, "إدارة سمات واجهة المستخدم")
        self.theme_manager = theme_manager if theme_manager else ThemeManager()
        self.app = app
        self.resize(800, 600)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # إنشاء تبويبات
        tabs = QTabWidget()
        
        # تبويب اختيار السمة
        select_tab = QWidget()
        select_layout = QVBoxLayout(select_tab)
        
        # مجموعة اختيار السمة
        theme_group = QGroupBox("اختيار السمة")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(self.theme_manager.themes.keys())
        self.theme_combo.setCurrentText(self.theme_manager.current_theme)
        self.theme_combo.currentTextChanged.connect(self.preview_theme)
        
        theme_layout.addRow("السمة:", self.theme_combo)
        select_layout.addWidget(theme_group)
        
        # مجموعة معاينة السمة
        preview_group = QGroupBox("معاينة السمة")
        preview_layout = QVBoxLayout(preview_group)
        
        # إضافة عناصر للمعاينة
        preview_form = QFormLayout()
        preview_form.addRow("حقل نص:", QLabel("هذا نص للمعاينة"))
        
        preview_button = QPushButton("زر للمعاينة")
        preview_form.addRow("زر:", preview_button)
        
        preview_layout.addLayout(preview_form)
        select_layout.addWidget(preview_group)
        
        # تبويب تخصيص السمة
        customize_tab = QWidget()
        customize_layout = QVBoxLayout(customize_tab)
        
        # مجموعة الألوان
        colors_group = QGroupBox("الألوان")
        colors_layout = QGridLayout(colors_group)
        
        self.color_buttons = {}
        
        # إنشاء أزرار اختيار الألوان
        color_fields = [
            ("primary_color", "اللون الرئيسي"),
            ("secondary_color", "اللون الثانوي"),
            ("background_color", "لون الخلفية"),
            ("text_color", "لون النص"),
            ("button_color", "لون الأزرار"),
            ("button_text_color", "لون نص الأزرار"),
            ("accent_color", "لون التمييز")
        ]
        
        for i, (field, label) in enumerate(color_fields):
            colors_layout.addWidget(QLabel(label), i, 0)
            
            color_button = QPushButton()
            color_button.setFixedSize(QSize(50, 30))
            color_button.clicked.connect(lambda checked, f=field: self.choose_color(f))
            self.color_buttons[field] = color_button
            
            colors_layout.addWidget(color_button, i, 1)
        
        customize_layout.addWidget(colors_group)
        
        # مجموعة الخط
        font_group = QGroupBox("الخط")
        font_layout = QFormLayout(font_group)
        
        self.font_combo = QComboBox()
        self.font_combo.addItems(["Arial", "Times New Roman", "Courier New", "Tahoma", "Verdana"])
        font_layout.addRow("نوع الخط:", self.font_combo)
        
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setMinimum(8)
        self.font_size_slider.setMaximum(16)
        self.font_size_slider.setValue(10)
        self.font_size_slider.setTickPosition(QSlider.TicksBelow)
        self.font_size_slider.setTickInterval(1)
        
        self.font_size_label = QLabel("10")
        self.font_size_slider.valueChanged.connect(lambda v: self.font_size_label.setText(str(v)))
        
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(self.font_size_slider)
        font_size_layout.addWidget(self.font_size_label)
        
        font_layout.addRow("حجم الخط:", font_size_layout)
        customize_layout.addWidget(font_group)
        
        # مجموعة حفظ السمة
        save_group = QGroupBox("حفظ السمة")
        save_layout = QFormLayout(save_group)
        
        self.theme_name_edit = QComboBox()
        self.theme_name_edit.setEditable(True)
        self.theme_name_edit.addItems(self.theme_manager.themes.keys())
        self.theme_name_edit.setCurrentText(self.theme_manager.current_theme)
        self.theme_name_edit.currentTextChanged.connect(self.load_theme_for_editing)
        
        save_layout.addRow("اسم السمة:", self.theme_name_edit)
        
        save_btn = QPushButton("حفظ السمة")
        save_btn.clicked.connect(self.save_custom_theme)
        
        delete_btn = QPushButton("حذف السمة")
        delete_btn.clicked.connect(self.delete_theme)
        delete_btn.setStyleSheet("background-color: #e74c3c;")
        
        btn_layout = QHBoxLayout()
        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(delete_btn)
        
        save_layout.addRow("", btn_layout)
        customize_layout.addWidget(save_group)
        
        # إضافة التبويبات
        tabs.addTab(select_tab, "اختيار السمة")
        tabs.addTab(customize_tab, "تخصيص السمة")
        
        layout.addWidget(tabs)
        
        # أزرار أسفل النافذة
        btn_layout = QHBoxLayout()
        
        apply_btn = QPushButton("تطبيق")
        apply_btn.clicked.connect(self.apply_theme)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)
        
        btn_layout.addWidget(apply_btn)
        btn_layout.addWidget(close_btn)
        
        layout.addLayout(btn_layout)
        
        # تحميل بيانات السمة الحالية
        self.load_current_theme()
        
    def load_current_theme(self):
        """تحميل بيانات السمة الحالية"""
        theme_data = self.theme_manager.get_current_theme_data()
        self.update_color_buttons(theme_data)
        
        # تحديث عناصر الخط
        self.font_combo.setCurrentText(theme_data.get("font_family", "Arial"))
        self.font_size_slider.setValue(theme_data.get("font_size", 10))
        
    def load_theme_for_editing(self, theme_name):
        """تحميل بيانات السمة للتعديل"""
        if theme_name in self.theme_manager.themes:
            theme_data = self.theme_manager.themes[theme_name]
            self.update_color_buttons(theme_data)
            
            # تحديث عناصر الخط
            self.font_combo.setCurrentText(theme_data.get("font_family", "Arial"))
            self.font_size_slider.setValue(theme_data.get("font_size", 10))
    
    def update_color_buttons(self, theme_data):
        """تحديث ألوان الأزرار بناءً على بيانات السمة"""
        for field, button in self.color_buttons.items():
            color = QColor(theme_data.get(field, "#000000"))
            button.setStyleSheet(f"background-color: {color.name()};")
            
    def choose_color(self, field):
        """فتح مربع حوار اختيار اللون"""
        current_color = QColor(self.color_buttons[field].palette().button().color())
        color = QColorDialog.getColor(current_color, self, f"اختر {field}")
        
        if color.isValid():
            self.color_buttons[field].setStyleSheet(f"background-color: {color.name()};")
            
    def get_current_theme_values(self):
        """الحصول على قيم السمة الحالية من واجهة المستخدم"""
        theme_data = {}
        
        # جمع الألوان
        for field, button in self.color_buttons.items():
            style = button.styleSheet()
            color = style.split("background-color:")[1].split(";")[0].strip()
            theme_data[field] = color
            
        # جمع إعدادات الخط
        theme_data["font_family"] = self.font_combo.currentText()
        theme_data["font_size"] = self.font_size_slider.value()
        
        return theme_data
        
    def save_custom_theme(self):
        """حفظ السمة المخصصة"""
        theme_name = self.theme_name_edit.currentText().strip()
        
        if not theme_name:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم للسمة.")
            return
            
        theme_data = self.get_current_theme_values()
        self.theme_manager.add_theme(theme_name, theme_data)
        
        # تحديث قائمة السمات
        current_index = self.theme_combo.findText(theme_name)
        if current_index == -1:
            self.theme_combo.addItem(theme_name)
            self.theme_combo.setCurrentText(theme_name)
            
        QMessageBox.information(self, "نجاح", f"تم حفظ السمة '{theme_name}' بنجاح.")
        
    def delete_theme(self):
        """حذف السمة المحددة"""
        theme_name = self.theme_name_edit.currentText()
        
        if theme_name in ["الافتراضي", "داكن", "عالي التباين"]:
            QMessageBox.warning(self, "تحذير", "لا يمكن حذف السمات الافتراضية.")
            return
            
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف السمة '{theme_name}'؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.theme_manager.remove_theme(theme_name):
                # تحديث قوائم السمات
                index = self.theme_combo.findText(theme_name)
                if index != -1:
                    self.theme_combo.removeItem(index)
                    
                index = self.theme_name_edit.findText(theme_name)
                if index != -1:
                    self.theme_name_edit.removeItem(index)
                    
                QMessageBox.information(self, "نجاح", f"تم حذف السمة '{theme_name}' بنجاح.")
                
                # إذا كانت السمة المحذوفة هي السمة الحالية، نعود للسمة الافتراضية
                if theme_name == self.theme_manager.current_theme:
                    self.theme_manager.save_current_theme("الافتراضي")
                    self.theme_combo.setCurrentText("الافتراضي")
            else:
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف هذه السمة.")
                
    def preview_theme(self, theme_name):
        """معاينة السمة المحددة"""
        if theme_name in self.theme_manager.themes:
            # تحديث عناصر التخصيص بناءً على السمة المحددة
            self.theme_name_edit.setCurrentText(theme_name)
            self.load_theme_for_editing(theme_name)
            
    def apply_theme(self):
        """تطبيق السمة المحددة"""
        theme_name = self.theme_combo.currentText()
        self.theme_manager.save_current_theme(theme_name)
        
        if self.app:
            self.theme_manager.apply_theme(self.app)
            
        QMessageBox.information(self, "نجاح", f"تم تطبيق السمة '{theme_name}' بنجاح.")
