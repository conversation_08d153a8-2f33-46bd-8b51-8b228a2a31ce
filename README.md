# 🏛️ نظام إدارة الأنشطة المتقدم

نظام شامل ومتطور لإدارة الأنشطة والفعاليات مع واجهة مستخدم عصرية وميزات متقدمة.

## ✨ الميزات الرئيسية

### 📊 إدارة الأنشطة
- إضافة وتعديل وحذف الأنشطة
- تصنيف متقدم للأنشطة
- تتبع المشاركين والميزانيات
- إدارة المواقع الجغرافية

### 📈 التقارير والإحصائيات
- تقارير ملخصة ومفصلة
- رسوم بيانية تفاعلية
- تحليل جغرافي وزمني
- إحصائيات الأداء المالي

### 🎨 واجهة المستخدم العصرية
- تصميم حديث مع ألوان متدرجة
- دعم كامل للغة العربية
- واجهة سهلة الاستخدام
- تبويبات منظمة

### 📊 لوحة المعلومات التفاعلية
- مؤشرات أداء رئيسية (KPIs)
- رسوم بيانية حية
- إحصائيات فورية
- تحديث تلقائي للبيانات

### 🔄 الاستيراد والتصدير
- استيراد من Excel و CSV
- تصدير إلى Excel, PDF, Word
- **تصدير القوالب الجاهزة** (ميزة جديدة!)
- التحقق من صحة البيانات
- معاينة قبل الاستيراد

### 💾 النسخ الاحتياطي والاستعادة
- نسخ احتياطية كاملة وتزايدية
- جدولة تلقائية للنسخ
- استعادة آمنة للبيانات
- ضغط وتشفير الملفات

### ⚡ مراقبة الأداء
- مراقبة استخدام الذاكرة والمعالج
- تحسين قاعدة البيانات التلقائي
- إدارة الذاكرة الذكية
- سجل الأخطاء والأحداث

### ❓ المساعدة والتوثيق
- دليل مستخدم شامل
- أسئلة شائعة
- مساعدة تفاعلية
- معلومات النظام والدعم

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11, macOS 10.14+, Linux
- الذاكرة: 4 GB RAM (8 GB مستحسن)
- مساحة القرص: 500 MB

### خطوات التثبيت

1. **تحميل الملفات:**
   ```bash
   git clone [repository-url]
   cd activities-system
   ```

2. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل التطبيق:**
   ```bash
   python run_app.py
   ```

### التشغيل السريع
يمكنك تشغيل التطبيق مباشرة باستخدام:
```bash
python run_app.py
```

سيقوم الملف بفحص وتثبيت المتطلبات تلقائياً.

### بناء ملف تنفيذي (.exe)
لإنشاء ملف تنفيذي يعمل بدون Python:

**الطريقة السريعة:**
```bash
# في Windows
build.bat

# أو مباشرة
python build_exe.py
```

**متطلبات البناء:**
```bash
pip install -r requirements_build.txt
```

**النتيجة:**
- ملف تنفيذي في مجلد `dist/`
- ملف تثبيت تلقائي
- دليل استخدام للمستخدم النهائي

لمزيد من التفاصيل، راجع **[دليل البناء](BUILD_GUIDE.md)**

## 📁 هيكل المشروع

```
📁 نظام إدارة الأنشطة/
├── 📄 run_app.py (ملف التشغيل الرئيسي)
├── 📄 ac.py (الملف الرئيسي للتطبيق)
├── 📄 ModernUI.py (واجهة المستخدم العصرية)
├── 📄 ReportsModule.py (نظام التقارير المتقدم)
├── 📄 ExportModule.py (نظام التصدير الشامل)
├── 📄 ImportModule.py (نظام الاستيراد الذكي)
├── 📄 DashboardModule.py (لوحة المعلومات التفاعلية)
├── 📄 BackupModule.py (النسخ الاحتياطي والاستعادة)
├── 📄 PerformanceModule.py (مراقبة الأداء والتحسين)
├── 📄 HelpModule.py (المساعدة والتوثيق)
├── 📄 requirements.txt (متطلبات المشروع)
├── 📄 README.md (دليل المستخدم)
└── 📄 activities.db (قاعدة البيانات)
```

## 🎯 كيفية الاستخدام

### البدء السريع
1. شغل التطبيق باستخدام `python run_app.py`
2. انقر على "إضافة نشاط" لإضافة نشاط جديد
3. استخدم تبويب "عرض البيانات" لاستعراض الأنشطة
4. انتقل إلى "التقارير المتقدمة" لإنتاج التقارير
5. راجع "لوحة المعلومات" للإحصائيات الحية

### إدارة الأنشطة
- **إضافة نشاط:** انقر على زر "إضافة نشاط" واملأ البيانات المطلوبة
- **تعديل نشاط:** حدد النشاط من الجدول وانقر على "تعديل"
- **حذف نشاط:** حدد النشاط وانقر على "حذف" مع التأكيد

### إنتاج التقارير
1. انتقل إلى تبويب "التقارير المتقدمة"
2. اختر نوع التقرير المطلوب
3. حدد الفلاتر (التاريخ، التصنيف، المنطقة)
4. انقر على "إنتاج التقرير"
5. اختر تنسيق التصدير المطلوب

### تصدير القوالب الجاهزة (ميزة جديدة!)
1. انتقل إلى تبويب "التصدير المتقدم"
2. اختر تبويب "تصدير القوالب"
3. حدد القالب المطلوب من القائمة:
   - 📊 قالب إدخال الأنشطة
   - 📈 قالب تقرير الأداء
   - 🗺️ قالب التوزيع الجغرافي
   - 💰 قالب التقرير المالي
   - 📅 قالب التقرير الشهري
   - 👥 قالب تقرير المشاركين
4. اختر تنسيق التصدير والخيارات الإضافية
5. انقر على "تصدير القالب" أو "تصدير جميع القوالب"

### النسخ الاحتياطي
1. انتقل إلى تبويب "النسخ الاحتياطي"
2. اختر نوع النسخة (كاملة، تزايدية، تفاضلية)
3. حدد مسار الحفظ
4. انقر على "بدء النسخ الاحتياطي"

## 📋 ميزة تصدير القوالب الجاهزة

### 🎯 ما هي القوالب؟
القوالب هي ملفات جاهزة ومُعدة مسبقاً تساعدك في:
- **توحيد عملية إدخال البيانات**
- **إنتاج تقارير احترافية بسرعة**
- **ضمان اكتمال جميع البيانات المطلوبة**
- **توفير الوقت والجهد**

### 📊 القوالب المتاحة

| القالب | الوصف | الاستخدام |
|---------|--------|-----------|
| 📊 قالب إدخال الأنشطة | قالب فارغ لإدخال أنشطة جديدة | إدخال البيانات المنظم |
| 📈 قالب تقرير الأداء | مؤشرات الأداء الرئيسية | تقييم الأداء |
| 🗺️ قالب التوزيع الجغرافي | توزيع الأنشطة حسب المناطق | التحليل الجغرافي |
| 💰 قالب التقرير المالي | تحليل الميزانيات والتكاليف | المراقبة المالية |
| 📅 قالب التقرير الشهري | تقرير شهري شامل | التقارير الدورية |
| 👥 قالب تقرير المشاركين | إحصائيات المشاركين | تحليل الفئات المستهدفة |

### 🚀 مزايا استخدام القوالب
- **توفير الوقت:** لا حاجة لإنشاء التقارير من الصفر
- **التوحيد القياسي:** ضمان تناسق التقارير
- **سهولة الاستخدام:** قوالب جاهزة مع تعليمات واضحة
- **مرونة التخصيص:** يمكن تعديل القوالب حسب الحاجة
- **دعم متعدد التنسيقات:** Excel, PDF, Word, CSV

### 📖 دليل مفصل
لمزيد من التفاصيل حول استخدام ميزة تصدير القوالب، راجع:
**[دليل تصدير القوالب](Templates_Guide.md)**

## 🔧 حل المشاكل الشائعة

### مشاكل التثبيت
- **خطأ في تثبيت PyQt5:** استخدم `pip install PyQt5==5.15.7`
- **مشكلة في matplotlib:** تأكد من تثبيت `pip install matplotlib>=3.5.0`

### مشاكل التشغيل
- **بطء في التطبيق:** استخدم أدوات تحسين قاعدة البيانات في تبويب "مراقبة الأداء"
- **مشاكل الخط العربي:** التحذيرات طبيعية ولا تؤثر على عمل التطبيق

### مشاكل البيانات
- **فقدان البيانات:** استعد من آخر نسخة احتياطية
- **أخطاء في الاستيراد:** تحقق من تنسيق الملف وصحة البيانات

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-11-1234567
- 🌐 الموقع الإلكتروني: www.activities-system.com

## 📄 الترخيص

هذا البرنامج مرخص للاستخدام الداخلي. جميع الحقوق محفوظة.

## 🎉 شكر وتقدير

شكراً لاستخدام نظام إدارة الأنشطة المتقدم. نتطلع لتقييمكم وملاحظاتكم لتحسين النظام.

---

**الإصدار:** 2.0  
**تاريخ الإصدار:** ديسمبر 2024  
**المطور:** فريق التطوير المتخصص
