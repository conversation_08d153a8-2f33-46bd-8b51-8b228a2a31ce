"""
وحدة النسخ الاحتياطي والاستعادة المتقدمة
تدعم النسخ الاحتياطي التلقائي والاستعادة الآمنة للبيانات
"""

import sqlite3
import os
import shutil
import zipfile
import json
from datetime import datetime, timedelta
import threading
import schedule
import time
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFileDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QComboBox, QCheckBox, QGroupBox, QFormLayout, QTextEdit,
    QProgressBar, QMessageBox, QTabWidget, QSplitter,
    QListWidget, QListWidgetItem, QFrame, QScrollArea,
    QSpinBox, QLineEdit, QDateTimeEdit, QSlider
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer, QDateTime
from PyQt5.QtGui import QFont, QColor, QIcon
import hashlib

class BackupWorker(QThread):
    """عامل النسخ الاحتياطي في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    backup_completed = pyqtSignal(str, str)  # backup_path, backup_info
    backup_failed = pyqtSignal(str)
    
    def __init__(self, conn, backup_config):
        super().__init__()
        self.conn = conn
        self.backup_config = backup_config
        
    def run(self):
        try:
            self.status_updated.emit("بدء عملية النسخ الاحتياطي...")
            self.progress_updated.emit(10)
            
            backup_type = self.backup_config.get('type', 'full')
            backup_path = self.backup_config.get('path', 'backups')
            include_attachments = self.backup_config.get('include_attachments', True)
            compress = self.backup_config.get('compress', True)
            
            # إنشاء مجلد النسخ الاحتياطية
            if not os.path.exists(backup_path):
                os.makedirs(backup_path)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{backup_type}_{timestamp}"
            
            if backup_type == 'full':
                backup_file = self.create_full_backup(backup_path, backup_name)
            elif backup_type == 'incremental':
                backup_file = self.create_incremental_backup(backup_path, backup_name)
            elif backup_type == 'differential':
                backup_file = self.create_differential_backup(backup_path, backup_name)
            
            self.progress_updated.emit(80)
            
            # ضغط النسخة الاحتياطية
            if compress:
                self.status_updated.emit("ضغط النسخة الاحتياطية...")
                compressed_file = self.compress_backup(backup_file)
                if compressed_file:
                    os.remove(backup_file)
                    backup_file = compressed_file
            
            self.progress_updated.emit(90)
            
            # إنشاء معلومات النسخة الاحتياطية
            backup_info = self.create_backup_info(backup_file, backup_type)
            
            self.progress_updated.emit(100)
            self.backup_completed.emit(backup_file, backup_info)
            
        except Exception as e:
            self.backup_failed.emit(str(e))
    
    def create_full_backup(self, backup_path, backup_name):
        """إنشاء نسخة احتياطية كاملة"""
        self.status_updated.emit("إنشاء نسخة احتياطية كاملة...")
        self.progress_updated.emit(30)
        
        backup_file = os.path.join(backup_path, f"{backup_name}.db")
        
        # نسخ قاعدة البيانات
        cursor = self.conn.cursor()
        cursor.execute(f"VACUUM INTO '{backup_file}'")
        
        self.progress_updated.emit(60)
        return backup_file
    
    def create_incremental_backup(self, backup_path, backup_name):
        """إنشاء نسخة احتياطية تزايدية"""
        self.status_updated.emit("إنشاء نسخة احتياطية تزايدية...")
        self.progress_updated.emit(30)
        
        # البحث عن آخر نسخة احتياطية
        last_backup_date = self.get_last_backup_date(backup_path)
        
        backup_file = os.path.join(backup_path, f"{backup_name}.json")
        
        # استخراج البيانات المحدثة منذ آخر نسخة احتياطية
        cursor = self.conn.cursor()
        
        if last_backup_date:
            cursor.execute("""
                SELECT * FROM activities 
                WHERE updated_at > ? OR created_at > ?
            """, (last_backup_date, last_backup_date))
        else:
            cursor.execute("SELECT * FROM activities")
        
        activities = cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        cursor.execute("PRAGMA table_info(activities)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # تحويل إلى JSON
        backup_data = {
            'backup_type': 'incremental',
            'backup_date': datetime.now().isoformat(),
            'last_backup_date': last_backup_date,
            'columns': columns,
            'activities': activities
        }
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
        
        self.progress_updated.emit(60)
        return backup_file
    
    def create_differential_backup(self, backup_path, backup_name):
        """إنشاء نسخة احتياطية تفاضلية"""
        self.status_updated.emit("إنشاء نسخة احتياطية تفاضلية...")
        # تنفيذ مشابه للتزايدية ولكن مقارنة مع آخر نسخة كاملة
        return self.create_incremental_backup(backup_path, backup_name)
    
    def compress_backup(self, backup_file):
        """ضغط النسخة الاحتياطية"""
        try:
            compressed_file = backup_file + '.zip'
            
            with zipfile.ZipFile(compressed_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_file, os.path.basename(backup_file))
            
            return compressed_file
        except Exception as e:
            print(f"خطأ في ضغط النسخة الاحتياطية: {e}")
            return None
    
    def create_backup_info(self, backup_file, backup_type):
        """إنشاء معلومات النسخة الاحتياطية"""
        file_size = os.path.getsize(backup_file)
        file_hash = self.calculate_file_hash(backup_file)
        
        cursor = self.conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM activities")
        total_records = cursor.fetchone()[0]
        
        backup_info = {
            'file_path': backup_file,
            'backup_type': backup_type,
            'creation_date': datetime.now().isoformat(),
            'file_size': file_size,
            'file_hash': file_hash,
            'total_records': total_records,
            'database_version': self.get_database_version()
        }
        
        return json.dumps(backup_info, ensure_ascii=False, indent=2)
    
    def calculate_file_hash(self, file_path):
        """حساب hash للملف للتحقق من سلامته"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def get_database_version(self):
        """الحصول على إصدار قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("PRAGMA user_version")
            return cursor.fetchone()[0]
        except:
            return 1
    
    def get_last_backup_date(self, backup_path):
        """الحصول على تاريخ آخر نسخة احتياطية"""
        try:
            backup_files = [f for f in os.listdir(backup_path) if f.startswith('backup_')]
            if not backup_files:
                return None
            
            # ترتيب الملفات حسب التاريخ
            backup_files.sort(reverse=True)
            
            # استخراج التاريخ من اسم الملف
            latest_file = backup_files[0]
            date_part = latest_file.split('_')[-2] + '_' + latest_file.split('_')[-1].split('.')[0]
            return datetime.strptime(date_part, '%Y%m%d_%H%M%S').isoformat()
        except:
            return None


class RestoreWorker(QThread):
    """عامل الاستعادة في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    restore_completed = pyqtSignal(str)
    restore_failed = pyqtSignal(str)
    
    def __init__(self, conn, backup_file, restore_config):
        super().__init__()
        self.conn = conn
        self.backup_file = backup_file
        self.restore_config = restore_config
        
    def run(self):
        try:
            self.status_updated.emit("بدء عملية الاستعادة...")
            self.progress_updated.emit(10)
            
            # التحقق من سلامة النسخة الاحتياطية
            if not self.verify_backup_integrity():
                raise Exception("فشل في التحقق من سلامة النسخة الاحتياطية")
            
            self.progress_updated.emit(30)
            
            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            if self.restore_config.get('backup_current', True):
                self.status_updated.emit("إنشاء نسخة احتياطية من البيانات الحالية...")
                self.backup_current_data()
            
            self.progress_updated.emit(50)
            
            # تحديد نوع النسخة الاحتياطية
            if self.backup_file.endswith('.db') or self.backup_file.endswith('.db.zip'):
                self.restore_full_backup()
            elif self.backup_file.endswith('.json') or self.backup_file.endswith('.json.zip'):
                self.restore_incremental_backup()
            
            self.progress_updated.emit(90)
            
            # التحقق من نجاح الاستعادة
            self.verify_restore()
            
            self.progress_updated.emit(100)
            self.restore_completed.emit("تمت الاستعادة بنجاح")
            
        except Exception as e:
            self.restore_failed.emit(str(e))
    
    def verify_backup_integrity(self):
        """التحقق من سلامة النسخة الاحتياطية"""
        try:
            if self.backup_file.endswith('.zip'):
                # فك ضغط الملف مؤقتاً للتحقق
                with zipfile.ZipFile(self.backup_file, 'r') as zipf:
                    zipf.testzip()
                return True
            else:
                # التحقق من وجود الملف وإمكانية قراءته
                return os.path.exists(self.backup_file) and os.access(self.backup_file, os.R_OK)
        except:
            return False
    
    def backup_current_data(self):
        """إنشاء نسخة احتياطية من البيانات الحالية"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"pre_restore_backup_{timestamp}.db"
        
        cursor = self.conn.cursor()
        cursor.execute(f"VACUUM INTO '{backup_file}'")
    
    def restore_full_backup(self):
        """استعادة نسخة احتياطية كاملة"""
        self.status_updated.emit("استعادة النسخة الاحتياطية الكاملة...")
        
        # فك الضغط إذا كان الملف مضغوطاً
        restore_file = self.backup_file
        if self.backup_file.endswith('.zip'):
            with zipfile.ZipFile(self.backup_file, 'r') as zipf:
                zipf.extractall('temp_restore')
                restore_file = os.path.join('temp_restore', zipf.namelist()[0])
        
        # إغلاق الاتصال الحالي
        self.conn.close()
        
        # استبدال قاعدة البيانات
        original_db = "activities.db"
        backup_current = f"{original_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # نسخ قاعدة البيانات الحالية
        shutil.copy2(original_db, backup_current)
        
        # استبدال بالنسخة المستعادة
        shutil.copy2(restore_file, original_db)
        
        # إعادة الاتصال
        self.conn = sqlite3.connect(original_db)
        
        # تنظيف الملفات المؤقتة
        if restore_file != self.backup_file:
            os.remove(restore_file)
            os.rmdir('temp_restore')
    
    def restore_incremental_backup(self):
        """استعادة نسخة احتياطية تزايدية"""
        self.status_updated.emit("استعادة النسخة الاحتياطية التزايدية...")
        
        # فك الضغط إذا كان الملف مضغوطاً
        restore_file = self.backup_file
        if self.backup_file.endswith('.zip'):
            with zipfile.ZipFile(self.backup_file, 'r') as zipf:
                zipf.extractall('temp_restore')
                restore_file = os.path.join('temp_restore', zipf.namelist()[0])
        
        # قراءة بيانات النسخة الاحتياطية
        with open(restore_file, 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
        
        cursor = self.conn.cursor()
        
        # استعادة البيانات
        columns = backup_data['columns']
        activities = backup_data['activities']
        
        for activity in activities:
            # تحديث أو إدراج السجل
            placeholders = ', '.join(['?' for _ in columns])
            query = f"INSERT OR REPLACE INTO activities ({', '.join(columns)}) VALUES ({placeholders})"
            cursor.execute(query, activity)
        
        self.conn.commit()
        
        # تنظيف الملفات المؤقتة
        if restore_file != self.backup_file:
            os.remove(restore_file)
            os.rmdir('temp_restore')
    
    def verify_restore(self):
        """التحقق من نجاح الاستعادة"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM activities")
            count = cursor.fetchone()[0]
            
            if count == 0:
                raise Exception("لا توجد بيانات بعد الاستعادة")
            
            # التحقق من سلامة البيانات
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()[0]
            
            if result != "ok":
                raise Exception("فشل في التحقق من سلامة البيانات")
                
        except Exception as e:
            raise Exception(f"فشل في التحقق من الاستعادة: {str(e)}")


class AutoBackupScheduler:
    """مجدول النسخ الاحتياطي التلقائي"""
    
    def __init__(self, conn, backup_config):
        self.conn = conn
        self.backup_config = backup_config
        self.running = False
        self.thread = None
        
    def start_scheduler(self):
        """بدء المجدول"""
        if self.running:
            return
            
        self.running = True
        
        # جدولة النسخ الاحتياطي
        frequency = self.backup_config.get('frequency', 'daily')
        backup_time = self.backup_config.get('time', '02:00')
        
        if frequency == 'daily':
            schedule.every().day.at(backup_time).do(self.create_scheduled_backup)
        elif frequency == 'weekly':
            day = self.backup_config.get('day', 'sunday')
            schedule.every().week.at(backup_time).do(self.create_scheduled_backup)
        elif frequency == 'monthly':
            schedule.every().month.do(self.create_scheduled_backup)
        
        # تشغيل المجدول في خيط منفصل
        self.thread = threading.Thread(target=self.run_scheduler)
        self.thread.daemon = True
        self.thread.start()
    
    def stop_scheduler(self):
        """إيقاف المجدول"""
        self.running = False
        schedule.clear()
        
    def run_scheduler(self):
        """تشغيل المجدول"""
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def create_scheduled_backup(self):
        """إنشاء نسخة احتياطية مجدولة"""
        try:
            backup_worker = BackupWorker(self.conn, self.backup_config)
            backup_worker.run()  # تشغيل مباشر في المجدول
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي المجدول: {e}")


class BackupRestoreWidget(QWidget):
    """واجهة النسخ الاحتياطي والاستعادة"""

    def __init__(self, conn):
        super().__init__()
        self.conn = conn
        self.backup_worker = None
        self.restore_worker = None
        self.auto_scheduler = None
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("💾 نظام النسخ الاحتياطي والاستعادة")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #795548, stop:1 #5D4037);
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # تبويبات النسخ الاحتياطي
        tab_widget = QTabWidget()

        # تبويب النسخ الاحتياطي
        backup_tab = self.create_backup_tab()
        tab_widget.addTab(backup_tab, "النسخ الاحتياطي")

        # تبويب الاستعادة
        restore_tab = self.create_restore_tab()
        tab_widget.addTab(restore_tab, "الاستعادة")

        # تبويب النسخ التلقائي
        auto_backup_tab = self.create_auto_backup_tab()
        tab_widget.addTab(auto_backup_tab, "النسخ التلقائي")

        # تبويب إدارة النسخ
        manage_tab = self.create_manage_tab()
        tab_widget.addTab(manage_tab, "إدارة النسخ")

        layout.addWidget(tab_widget)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إعدادات النسخ الاحتياطي
        settings_group = QGroupBox("إعدادات النسخ الاحتياطي")
        settings_layout = QFormLayout()

        # نوع النسخة الاحتياطية
        self.backup_type_combo = QComboBox()
        self.backup_type_combo.addItems([
            "نسخة كاملة (Full)",
            "نسخة تزايدية (Incremental)",
            "نسخة تفاضلية (Differential)"
        ])
        settings_layout.addRow("نوع النسخة:", self.backup_type_combo)

        # مسار الحفظ
        backup_path_layout = QHBoxLayout()
        self.backup_path_edit = QLineEdit("backups")
        backup_path_layout.addWidget(self.backup_path_edit)

        browse_btn = QPushButton("استعراض")
        browse_btn.clicked.connect(self.browse_backup_path)
        backup_path_layout.addWidget(browse_btn)

        settings_layout.addRow("مسار الحفظ:", backup_path_layout)

        # خيارات إضافية
        self.compress_backup = QCheckBox("ضغط النسخة الاحتياطية")
        self.compress_backup.setChecked(True)
        settings_layout.addRow("", self.compress_backup)

        self.include_attachments = QCheckBox("تضمين المرفقات")
        self.include_attachments.setChecked(True)
        settings_layout.addRow("", self.include_attachments)

        self.verify_backup = QCheckBox("التحقق من سلامة النسخة")
        self.verify_backup.setChecked(True)
        settings_layout.addRow("", self.verify_backup)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # معلومات قاعدة البيانات
        info_group = QGroupBox("معلومات قاعدة البيانات")
        info_layout = QFormLayout()

        # عرض معلومات قاعدة البيانات
        self.update_database_info(info_layout)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # أزرار النسخ الاحتياطي
        buttons_layout = QHBoxLayout()

        quick_backup_btn = QPushButton("🚀 نسخ احتياطي سريع")
        quick_backup_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5CBF60, stop:1 #4CAF50);
            }
        """)
        quick_backup_btn.clicked.connect(self.quick_backup)
        buttons_layout.addWidget(quick_backup_btn)

        custom_backup_btn = QPushButton("⚙️ نسخ احتياطي مخصص")
        custom_backup_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #42A5F5, stop:1 #2196F3);
            }
        """)
        custom_backup_btn.clicked.connect(self.custom_backup)
        buttons_layout.addWidget(custom_backup_btn)

        layout.addLayout(buttons_layout)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_restore_tab(self):
        """إنشاء تبويب الاستعادة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # اختيار ملف الاستعادة
        file_group = QGroupBox("اختيار ملف الاستعادة")
        file_layout = QVBoxLayout()

        # مسار الملف
        file_path_layout = QHBoxLayout()
        self.restore_file_edit = QLineEdit()
        self.restore_file_edit.setPlaceholderText("اختر ملف النسخة الاحتياطية...")
        file_path_layout.addWidget(self.restore_file_edit)

        browse_restore_btn = QPushButton("استعراض")
        browse_restore_btn.clicked.connect(self.browse_restore_file)
        file_path_layout.addWidget(browse_restore_btn)

        file_layout.addLayout(file_path_layout)

        # معلومات النسخة الاحتياطية
        self.backup_info_text = QTextEdit()
        self.backup_info_text.setMaximumHeight(150)
        self.backup_info_text.setReadOnly(True)
        file_layout.addWidget(QLabel("معلومات النسخة الاحتياطية:"))
        file_layout.addWidget(self.backup_info_text)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # خيارات الاستعادة
        options_group = QGroupBox("خيارات الاستعادة")
        options_layout = QFormLayout()

        self.backup_current_before_restore = QCheckBox("إنشاء نسخة احتياطية قبل الاستعادة")
        self.backup_current_before_restore.setChecked(True)
        options_layout.addRow("", self.backup_current_before_restore)

        self.verify_restore_integrity = QCheckBox("التحقق من سلامة البيانات بعد الاستعادة")
        self.verify_restore_integrity.setChecked(True)
        options_layout.addRow("", self.verify_restore_integrity)

        self.restore_mode_combo = QComboBox()
        self.restore_mode_combo.addItems([
            "استبدال كامل",
            "دمج البيانات",
            "استعادة انتقائية"
        ])
        options_layout.addRow("وضع الاستعادة:", self.restore_mode_combo)

        options_group.setLayout(options_layout)
        layout.addWidget(options_group)

        # تحذير
        warning_label = QLabel("⚠️ تحذير: عملية الاستعادة ستؤثر على البيانات الحالية. تأكد من إنشاء نسخة احتياطية أولاً.")
        warning_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 10px;
                color: #856404;
                font-weight: bold;
            }
        """)
        warning_label.setWordWrap(True)
        layout.addWidget(warning_label)

        # زر الاستعادة
        restore_btn = QPushButton("🔄 بدء الاستعادة")
        restore_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FF9800, stop:1 #F57C00);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #FFB74D, stop:1 #FF9800);
            }
        """)
        restore_btn.clicked.connect(self.start_restore)
        layout.addWidget(restore_btn)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_auto_backup_tab(self):
        """إنشاء تبويب النسخ التلقائي"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إعدادات النسخ التلقائي
        auto_settings_group = QGroupBox("إعدادات النسخ التلقائي")
        auto_settings_layout = QFormLayout()

        # تفعيل النسخ التلقائي
        self.enable_auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        auto_settings_layout.addRow("", self.enable_auto_backup)

        # تكرار النسخ
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
        auto_settings_layout.addRow("التكرار:", self.backup_frequency)

        # وقت النسخ
        self.backup_time = QDateTimeEdit()
        self.backup_time.setDisplayFormat("HH:mm")
        self.backup_time.setTime(QDateTime.currentDateTime().time().addSecs(3600))  # بعد ساعة
        auto_settings_layout.addRow("وقت النسخ:", self.backup_time)

        # عدد النسخ المحفوظة
        self.max_backups = QSpinBox()
        self.max_backups.setMinimum(1)
        self.max_backups.setMaximum(100)
        self.max_backups.setValue(7)
        auto_settings_layout.addRow("عدد النسخ المحفوظة:", self.max_backups)

        auto_settings_group.setLayout(auto_settings_layout)
        layout.addWidget(auto_settings_group)

        # حالة النسخ التلقائي
        status_group = QGroupBox("حالة النسخ التلقائي")
        status_layout = QFormLayout()

        self.auto_backup_status = QLabel("متوقف")
        self.auto_backup_status.setStyleSheet("color: #f44336; font-weight: bold;")
        status_layout.addRow("الحالة:", self.auto_backup_status)

        self.next_backup_time = QLabel("غير محدد")
        status_layout.addRow("النسخة التالية:", self.next_backup_time)

        self.last_backup_time = QLabel("لا توجد")
        status_layout.addRow("آخر نسخة:", self.last_backup_time)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # أزرار التحكم
        control_buttons = QHBoxLayout()

        start_auto_btn = QPushButton("▶️ بدء النسخ التلقائي")
        start_auto_btn.clicked.connect(self.start_auto_backup)
        control_buttons.addWidget(start_auto_btn)

        stop_auto_btn = QPushButton("⏹️ إيقاف النسخ التلقائي")
        stop_auto_btn.clicked.connect(self.stop_auto_backup)
        control_buttons.addWidget(stop_auto_btn)

        test_auto_btn = QPushButton("🧪 اختبار النسخ التلقائي")
        test_auto_btn.clicked.connect(self.test_auto_backup)
        control_buttons.addWidget(test_auto_btn)

        layout.addLayout(control_buttons)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_manage_tab(self):
        """إنشاء تبويب إدارة النسخ"""
        widget = QWidget()
        layout = QVBoxLayout()

        # قائمة النسخ الاحتياطية
        backups_group = QGroupBox("النسخ الاحتياطية المتاحة")
        backups_layout = QVBoxLayout()

        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(6)
        self.backups_table.setHorizontalHeaderLabels([
            "اسم الملف", "النوع", "التاريخ", "الحجم", "السجلات", "الحالة"
        ])
        self.backups_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        backups_layout.addWidget(self.backups_table)

        # أزرار إدارة النسخ
        manage_buttons = QHBoxLayout()

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_backups_list)
        manage_buttons.addWidget(refresh_btn)

        verify_btn = QPushButton("✅ التحقق من السلامة")
        verify_btn.clicked.connect(self.verify_selected_backup)
        manage_buttons.addWidget(verify_btn)

        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.clicked.connect(self.delete_selected_backup)
        manage_buttons.addWidget(delete_btn)

        export_btn = QPushButton("📤 تصدير")
        export_btn.clicked.connect(self.export_selected_backup)
        manage_buttons.addWidget(export_btn)

        backups_layout.addLayout(manage_buttons)
        backups_group.setLayout(backups_layout)
        layout.addWidget(backups_group)

        # إحصائيات النسخ
        stats_group = QGroupBox("إحصائيات النسخ الاحتياطية")
        stats_layout = QFormLayout()

        self.total_backups_label = QLabel("0")
        stats_layout.addRow("إجمالي النسخ:", self.total_backups_label)

        self.total_size_label = QLabel("0 MB")
        stats_layout.addRow("الحجم الكلي:", self.total_size_label)

        self.oldest_backup_label = QLabel("لا توجد")
        stats_layout.addRow("أقدم نسخة:", self.oldest_backup_label)

        self.newest_backup_label = QLabel("لا توجد")
        stats_layout.addRow("أحدث نسخة:", self.newest_backup_label)

        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        widget.setLayout(layout)
        return widget

    def update_database_info(self, layout):
        """تحديث معلومات قاعدة البيانات"""
        try:
            cursor = self.conn.cursor()

            # عدد السجلات
            cursor.execute("SELECT COUNT(*) FROM activities")
            total_records = cursor.fetchone()[0]
            layout.addRow("عدد السجلات:", QLabel(str(total_records)))

            # حجم قاعدة البيانات
            db_size = os.path.getsize("activities.db")
            size_mb = db_size / (1024 * 1024)
            layout.addRow("حجم قاعدة البيانات:", QLabel(f"{size_mb:.2f} MB"))

            # آخر تحديث
            cursor.execute("SELECT MAX(updated_at) FROM activities")
            last_update = cursor.fetchone()[0]
            layout.addRow("آخر تحديث:", QLabel(str(last_update) if last_update else "غير محدد"))

        except Exception as e:
            layout.addRow("خطأ:", QLabel(str(e)))

    def browse_backup_path(self):
        """استعراض مسار النسخ الاحتياطي"""
        path = QFileDialog.getExistingDirectory(self, "اختيار مجلد النسخ الاحتياطية")
        if path:
            self.backup_path_edit.setText(path)

    def browse_restore_file(self):
        """استعراض ملف الاستعادة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار ملف النسخة الاحتياطية",
            "", "Backup Files (*.db *.db.zip *.json *.json.zip);;All Files (*)"
        )
        if file_path:
            self.restore_file_edit.setText(file_path)
            self.load_backup_info(file_path)

    def load_backup_info(self, file_path):
        """تحميل معلومات النسخة الاحتياطية"""
        try:
            info_text = f"ملف النسخة الاحتياطية: {os.path.basename(file_path)}\n"
            info_text += f"الحجم: {os.path.getsize(file_path) / (1024*1024):.2f} MB\n"
            info_text += f"تاريخ الإنشاء: {datetime.fromtimestamp(os.path.getctime(file_path))}\n"

            if file_path.endswith('.json') or file_path.endswith('.json.zip'):
                info_text += "النوع: نسخة تزايدية/تفاضلية\n"
            else:
                info_text += "النوع: نسخة كاملة\n"

            self.backup_info_text.setText(info_text)

        except Exception as e:
            self.backup_info_text.setText(f"خطأ في قراءة معلومات الملف: {str(e)}")

    def quick_backup(self):
        """نسخ احتياطي سريع"""
        config = {
            'type': 'full',
            'path': 'backups',
            'compress': True,
            'include_attachments': True
        }
        self.start_backup(config)

    def custom_backup(self):
        """نسخ احتياطي مخصص"""
        backup_type_map = {
            0: 'full',
            1: 'incremental',
            2: 'differential'
        }

        config = {
            'type': backup_type_map[self.backup_type_combo.currentIndex()],
            'path': self.backup_path_edit.text(),
            'compress': self.compress_backup.isChecked(),
            'include_attachments': self.include_attachments.isChecked()
        }
        self.start_backup(config)

    def start_backup(self, config):
        """بدء عملية النسخ الاحتياطي"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            self.backup_worker = BackupWorker(self.conn, config)
            self.backup_worker.progress_updated.connect(self.progress_bar.setValue)
            self.backup_worker.status_updated.connect(self.status_label.setText)
            self.backup_worker.backup_completed.connect(self.backup_completed)
            self.backup_worker.backup_failed.connect(self.backup_failed)

            self.backup_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء النسخ الاحتياطي: {str(e)}")

    def start_restore(self):
        """بدء عملية الاستعادة"""
        backup_file = self.restore_file_edit.text()

        if not backup_file:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف النسخة الاحتياطية أولاً")
            return

        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            "هل أنت متأكد من أنك تريد استعادة هذه النسخة الاحتياطية؟\n"
            "سيتم استبدال البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                config = {
                    'backup_current': self.backup_current_before_restore.isChecked(),
                    'verify_integrity': self.verify_restore_integrity.isChecked(),
                    'restore_mode': self.restore_mode_combo.currentText()
                }

                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)

                self.restore_worker = RestoreWorker(self.conn, backup_file, config)
                self.restore_worker.progress_updated.connect(self.progress_bar.setValue)
                self.restore_worker.status_updated.connect(self.status_label.setText)
                self.restore_worker.restore_completed.connect(self.restore_completed)
                self.restore_worker.restore_failed.connect(self.restore_failed)

                self.restore_worker.start()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في بدء الاستعادة: {str(e)}")

    def backup_completed(self, backup_file, backup_info):
        """اكتمال النسخ الاحتياطي"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("تم إنشاء النسخة الاحتياطية بنجاح")

        # عرض معلومات النسخة الاحتياطية
        info_dict = json.loads(backup_info) if isinstance(backup_info, str) else backup_info
        size_mb = info_dict.get('file_size', 0) / (1024 * 1024)

        QMessageBox.information(self, "نجح",
            f"تم إنشاء النسخة الاحتياطية بنجاح:\n"
            f"الملف: {backup_file}\n"
            f"الحجم: {size_mb:.2f} MB\n"
            f"السجلات: {info_dict.get('total_records', 0)}")
        self.refresh_backups_list()

    def backup_failed(self, error):
        """فشل النسخ الاحتياطي"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل النسخ الاحتياطي")
        QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{error}")

    def restore_completed(self, message):
        """اكتمال الاستعادة"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("تمت الاستعادة بنجاح")
        QMessageBox.information(self, "نجح", message)

    def restore_failed(self, error):
        """فشل الاستعادة"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("فشل الاستعادة")
        QMessageBox.critical(self, "خطأ", f"فشل في الاستعادة:\n{error}")

    def start_auto_backup(self):
        """بدء النسخ التلقائي"""
        if not self.enable_auto_backup.isChecked():
            QMessageBox.warning(self, "تحذير", "يرجى تفعيل النسخ التلقائي أولاً")
            return

        config = {
            'frequency': ['daily', 'weekly', 'monthly'][self.backup_frequency.currentIndex()],
            'time': self.backup_time.time().toString("HH:mm"),
            'type': 'full',
            'path': 'auto_backups',
            'compress': True
        }

        self.auto_scheduler = AutoBackupScheduler(self.conn, config)
        self.auto_scheduler.start_scheduler()

        self.auto_backup_status.setText("يعمل")
        self.auto_backup_status.setStyleSheet("color: #4CAF50; font-weight: bold;")

    def stop_auto_backup(self):
        """إيقاف النسخ التلقائي"""
        if self.auto_scheduler:
            self.auto_scheduler.stop_scheduler()

        self.auto_backup_status.setText("متوقف")
        self.auto_backup_status.setStyleSheet("color: #f44336; font-weight: bold;")

    def test_auto_backup(self):
        """اختبار النسخ التلقائي"""
        QMessageBox.information(self, "اختبار", "سيتم تشغيل نسخة احتياطية تجريبية...")
        self.quick_backup()

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        # سيتم تنفيذها لاحقاً
        pass

    def verify_selected_backup(self):
        """التحقق من سلامة النسخة المحددة"""
        QMessageBox.information(self, "معلومات", "ميزة التحقق من السلامة قيد التطوير")

    def delete_selected_backup(self):
        """حذف النسخة المحددة"""
        QMessageBox.information(self, "معلومات", "ميزة حذف النسخ قيد التطوير")

    def export_selected_backup(self):
        """تصدير النسخة المحددة"""
        QMessageBox.information(self, "معلومات", "ميزة تصدير النسخ قيد التطوير")
